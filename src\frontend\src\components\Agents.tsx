import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from './Layout';
import AgentCard from './AgentCard';
import ActionCard from './ActionCard';
import ProfileAvatar from './ProfileAvatar';
import SparklesIcon from './SparklesIcon';
import AgentIcon from './AgentIcon';
import ArrowIcon from './ArrowIcon';
import styles from './Agents.module.css';

interface Agent {
  id: string;
  label: string;
  description: string;
}

const Agents: React.FC = () => {
  const navigate = useNavigate();
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);

  const handleAgentClick = (agentId: string) => {
    const agent = agents.find(a => a.id === agentId);
    if (agent) {
      setSelectedAgent(agent);
    }
  };

  const handleCloseModal = () => {
    setSelectedAgent(null);
  };

  const handleStartChat = () => {
    console.log(`Starting chat with: ${selectedAgent?.label}`);
    navigate('/chat');
  };

  const handleProfileClick = () => {
    console.log('Profile clicked');
    // Handle profile menu or navigation
  };

  const agents: Agent[] = [
    {
      id: 'knowledge-assistant',
      label: 'Knowledge Assistant',
      description: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.'
    },
    {
      id: 'sentiment-analysis',
      label: 'Sentiment Analysis',
      description: 'Analyze the emotional tone and sentiment of text content to understand customer feedback, social media posts, and communication patterns.'
    },
    {
      id: 'mailroom-agent',
      label: 'Mailroom Agent',
      description: 'Efficiently process, categorize, and route incoming mail and documents to the appropriate departments and personnel.'
    },
    {
      id: 'foi-agent',
      label: 'FOI Agent',
      description: 'Handle Freedom of Information requests by identifying relevant documents, ensuring compliance, and managing response timelines.'
    },
  ];

  return (
    <Layout currentPage="/agents">
      {/* Main Content Container */}
      <div className={styles.mainContent}>
        {/* Header */}
        <header className={styles.header}>
          <span className={styles.sparkles} aria-hidden="true">
            <SparklesIcon />
          </span>
          <h1 className={styles.title}>What can I help with?</h1>
        </header>

        {/* Agent Row */}
        <section className={styles.agentRow} aria-label="Capabilities">
          {agents.map((agent) => (
            <AgentCard
              key={agent.id}
              label={agent.label}
              onClick={() => handleAgentClick(agent.id)}
            />
          ))}
        </section>
      </div>

      {/* Profile Avatar */}
      <ProfileAvatar
        onClick={handleProfileClick}
        alt="User Profile"
      />

      {/* Agent Modal */}
      {selectedAgent && (
        <ActionCard
          title={selectedAgent.label}
          description={selectedAgent.description}
          buttonText="Start Chat"
          onButtonClick={handleStartChat}
          showCloseButton={true}
          onClose={handleCloseModal}
          buttonIcon={<ArrowIcon width={20} height={20} fill="white" />}
          titleIcon={<AgentIcon width={45} height={45} fill="white" />}
          isModal={true}
        />
      )}
    </Layout>
  );
};

export default Agents;
