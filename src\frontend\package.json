{"name": "frontend", "version": "1.0.0", "description": "React frontend for Azure AI Agents Demo", "main": "index.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "setup": "npm install -g pnpm@10.4.1 && pnpm install && pnpm build", "preview": "vite preview", "typecheck": "tsc --noEmit"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.4.1", "dependencies": {"@vitejs/plugin-react": "4.4.1", "clsx": "2.1.1", "copy-to-clipboard": "^3.3.3", "lucide-react": "^0.468.0", "react": "19.1.0", "react-dom": "19.1.0", "react-router-dom": "^6.28.0", "react-markdown": "10.1.0", "react-syntax-highlighter": "^15.5.0", "rehype-katex": "^7.0.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "rehype-stringify": "^10.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-supersub": "^1.0.0", "vite": "6.3.4", "prismjs": "1.30.0"}, "devDependencies": {"@types/node": "22.14.1", "@types/react": "18.3.20", "@types/react-dom": "18.3.5", "@types/react-syntax-highlighter": "15.5.13", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "5.8.3"}, "resolutions": {"prismjs": "1.30.0"}}