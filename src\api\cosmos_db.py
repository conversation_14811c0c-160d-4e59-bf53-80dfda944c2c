# Copyright (c) Microsoft. All rights reserved.
# Licensed under the MIT license. See LICENSE.md file in the project root for full license information.

import os
import uuid
import asyncio
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
import logging

from azure.cosmos.aio import CosmosClient
from azure.cosmos import PartitionKey
from azure.identity.aio import DefaultAzureCredential

logger = logging.getLogger(__name__)


class ChatSession:
    """Model for chat session data"""
    
    def __init__(
        self,
        session_id: str = None,
        user_id: str = None,
        agent_id: str = None,
        thread_id: str = None,
        title: str = None,
        created_at: datetime = None,
        updated_at: datetime = None,
        message_count: int = 0
    ):
        self.session_id = session_id or str(uuid.uuid4())
        self.user_id = user_id
        self.agent_id = agent_id
        self.thread_id = thread_id
        self.title = title or "New Chat"
        self.created_at = created_at or datetime.now(timezone.utc)
        self.updated_at = updated_at or datetime.now(timezone.utc)
        self.message_count = message_count

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.session_id,
            "session_id": self.session_id,
            "user_id": self.user_id,
            "agent_id": self.agent_id,
            "thread_id": self.thread_id,
            "title": self.title,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "message_count": self.message_count,
            "type": "session"
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatSession':
        return cls(
            session_id=data.get("session_id"),
            user_id=data.get("user_id"),
            agent_id=data.get("agent_id"),
            thread_id=data.get("thread_id"),
            title=data.get("title"),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
            updated_at=datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else None,
            message_count=data.get("message_count", 0)
        )


class ChatMessage:
    """Model for chat message data"""
    
    def __init__(
        self,
        message_id: str = None,
        session_id: str = None,
        role: str = None,
        content: str = None,
        created_at: datetime = None,
        annotations: List[Any] = None
    ):
        self.message_id = message_id or str(uuid.uuid4())
        self.session_id = session_id
        self.role = role
        self.content = content
        self.created_at = created_at or datetime.now(timezone.utc)
        self.annotations = annotations or []

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.message_id,
            "message_id": self.message_id,
            "session_id": self.session_id,
            "role": self.role,
            "content": self.content,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "annotations": self.annotations,
            "type": "message"
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatMessage':
        return cls(
            message_id=data.get("message_id"),
            session_id=data.get("session_id"),
            role=data.get("role"),
            content=data.get("content"),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
            annotations=data.get("annotations", [])
        )


class CosmosDBManager:
    """Manager for CosmosDB operations"""
    
    def __init__(self):
        self.client: Optional[CosmosClient] = None
        self.database = None
        self.container = None
        # Don't load environment variables in __init__ - they may not be loaded yet
        self._environment = None
        self._database_name = None
        self._container_name = None
        self._endpoint = None

    @property
    def environment(self) -> str:
        """Get environment, loading from env vars if needed"""
        if self._environment is None:
            self._environment = os.getenv("ENVIRONMENT", "development")
        return self._environment

    @property
    def database_name(self) -> str:
        """Get database name, loading from env vars if needed"""
        if self._database_name is None:
            self._database_name = os.getenv("COSMOS_DB_DATABASE_NAME", "chat_history")
        return self._database_name

    @property
    def container_name(self) -> str:
        """Get container name, loading from env vars if needed"""
        if self._container_name is None:
            self._container_name = os.getenv("COSMOS_DB_CONTAINER_NAME", "chat_data")
        return self._container_name

    @property
    def endpoint(self) -> Optional[str]:
        """Get CosmosDB endpoint, loading from env vars if needed"""
        if self._endpoint is None:
            endpoint = os.getenv("COSMOS_DB_ENDPOINT")
            if not endpoint:
                logger.warning(f"COSMOS_DB_ENDPOINT not set for environment: {self.environment}")
                self._endpoint = None
            else:
                self._endpoint = endpoint
        return self._endpoint
        
    async def initialize(self):
        """Initialize CosmosDB client and ensure database/container exist"""
        try:
            if not self.endpoint:
                logger.warning(f"No CosmosDB endpoint configured for environment: {self.environment}")
                return False

            logger.info(f"Initializing CosmosDB for environment: {self.environment}")
            logger.info(f"CosmosDB endpoint: {self.endpoint}")

            # Check if using local emulator
            if "localhost" in self.endpoint or "127.0.0.1" in self.endpoint:
                # Use the well-known emulator key for local development
                emulator_key = "C2y6yDjf5/R+ob0N8A7Cgv30VRDJIWEHLM+4QDU5DE2nQ9nDuVTqobD4b8mGGyPMbIZnqyMsEcaGQy67XIw/Jw=="
                self.client = CosmosClient(self.endpoint, credential=emulator_key)
                logger.info("Using Azure Cosmos DB Emulator for local development")
            else:
                # Use managed identity for authentication in production
                credential = DefaultAzureCredential()
                self.client = CosmosClient(self.endpoint, credential=credential)
                logger.info("Using Azure Managed Identity for CosmosDB authentication")
            
            # Create database if it doesn't exist
            self.database = await self.client.create_database_if_not_exists(
                id=self.database_name
            )
            
            # Create container if it doesn't exist
            self.container = await self.database.create_container_if_not_exists(
                id=self.container_name,
                partition_key=PartitionKey(path="/session_id"),
                offer_throughput=400
            )
            
            logger.info(f"CosmosDB initialized: {self.database_name}/{self.container_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize CosmosDB: {e}")
            if self.environment == "development":
                logger.error("Make sure Azure Cosmos DB Emulator is running at https://localhost:8081")
            else:
                logger.error("Check CosmosDB endpoint and authentication configuration")
            return False
    
    async def close(self):
        """Close CosmosDB client"""
        if self.client:
            await self.client.close()

    async def _retry_operation(self, operation, *args, **kwargs):
        """Simple retry mechanism for CosmosDB operations"""
        if not self.container:
            logger.warning("CosmosDB not initialized, operation skipped")
            return None

        for attempt in range(2):  # Try twice
            try:
                return await operation(*args, **kwargs)
            except Exception as e:
                if attempt == 0:  # First attempt failed
                    logger.warning(f"CosmosDB operation failed, retrying: {e}")
                    await asyncio.sleep(1)  # Wait 1 second before retry
                else:  # Second attempt failed
                    logger.error(f"CosmosDB operation failed after retry: {e}")
                    raise
    
    async def create_session(self, session: ChatSession) -> ChatSession:
        """Create a new chat session"""
        if not self.container:
            logger.warning("CosmosDB not initialized, session will not be persisted")
            return session
        try:
            await self._retry_operation(self.container.create_item, body=session.to_dict())
            logger.info(f"Created session: {session.session_id}")
            return session
        except Exception as e:
            logger.error(f"Failed to create session: {e}")
            # Return session anyway to allow app to continue
            return session
    
    async def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get a chat session by ID"""
        if not self.container:
            logger.warning("CosmosDB not initialized, cannot retrieve session")
            return None
        try:
            query = "SELECT * FROM c WHERE c.session_id = @session_id AND c.type = 'session'"
            parameters = [{"name": "@session_id", "value": session_id}]
            
            items = []
            async for item in self.container.query_items(
                query=query,
                parameters=parameters,
                partition_key=session_id
            ):
                items.append(item)
            
            if items:
                return ChatSession.from_dict(items[0])
            return None
            
        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")
            return None
    
    async def get_user_sessions(self, user_id: str, agent_id: str = None) -> List[ChatSession]:
        """Get all chat sessions for a user, optionally filtered by agent"""
        if not self.container:
            logger.warning("CosmosDB not initialized, cannot retrieve user sessions")
            return []
        try:
            if agent_id:
                query = """
                    SELECT * FROM c 
                    WHERE c.user_id = @user_id 
                    AND c.agent_id = @agent_id 
                    AND c.type = 'session'
                    ORDER BY c.updated_at DESC
                """
                parameters = [
                    {"name": "@user_id", "value": user_id},
                    {"name": "@agent_id", "value": agent_id}
                ]
            else:
                query = """
                    SELECT * FROM c 
                    WHERE c.user_id = @user_id 
                    AND c.type = 'session'
                    ORDER BY c.updated_at DESC
                """
                parameters = [{"name": "@user_id", "value": user_id}]
            
            sessions = []
            async for item in self.container.query_items(
                query=query,
                parameters=parameters
            ):
                sessions.append(ChatSession.from_dict(item))
            
            return sessions
            
        except Exception as e:
            logger.error(f"Failed to get user sessions: {e}")
            return []
    
    async def update_session(self, session: ChatSession) -> ChatSession:
        """Update a chat session"""
        if not self.container:
            logger.warning("CosmosDB not initialized, session update will not be persisted")
            return session
        try:
            session.updated_at = datetime.now(timezone.utc)
            await self.container.replace_item(
                item=session.session_id,
                body=session.to_dict()
            )
            logger.info(f"Updated session: {session.session_id}")
            return session
        except Exception as e:
            logger.error(f"Failed to update session: {e}")
            raise
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a chat session and all its messages"""
        try:
            # Delete all messages for this session
            query = "SELECT * FROM c WHERE c.session_id = @session_id AND c.type = 'message'"
            parameters = [{"name": "@session_id", "value": session_id}]
            
            async for item in self.container.query_items(
                query=query,
                parameters=parameters,
                partition_key=session_id
            ):
                await self.container.delete_item(
                    item=item["id"],
                    partition_key=session_id
                )
            
            # Delete the session
            await self.container.delete_item(
                item=session_id,
                partition_key=session_id
            )
            
            logger.info(f"Deleted session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete session {session_id}: {e}")
            return False
    
    async def add_message(self, message: ChatMessage) -> ChatMessage:
        """Add a message to a session"""
        if not self.container:
            logger.warning("CosmosDB not initialized, message will not be persisted")
            return message
        try:
            await self._retry_operation(self.container.create_item, body=message.to_dict())

            # Update session message count and timestamp
            session = await self.get_session(message.session_id)
            if session:
                session.message_count += 1
                session.updated_at = datetime.now(timezone.utc)
                await self.update_session(session)

            logger.info(f"Added message to session: {message.session_id}")
            return message

        except Exception as e:
            logger.error(f"Failed to add message: {e}")
            # Return message anyway to allow app to continue
            return message
    
    async def get_session_messages(self, session_id: str) -> List[ChatMessage]:
        """Get all messages for a session"""
        if not self.container:
            logger.warning("CosmosDB not initialized, cannot retrieve messages")
            return []
        try:
            query = """
                SELECT * FROM c
                WHERE c.session_id = @session_id
                AND c.type = 'message'
                ORDER BY c.created_at ASC
            """
            parameters = [{"name": "@session_id", "value": session_id}]

            messages = []
            async for item in self.container.query_items(
                query=query,
                parameters=parameters,
                partition_key=session_id
            ):
                messages.append(ChatMessage.from_dict(item))

            return messages

        except Exception as e:
            logger.error(f"Failed to get session messages: {e}")
            return []


# Global instance
cosmos_manager = CosmosDBManager()
