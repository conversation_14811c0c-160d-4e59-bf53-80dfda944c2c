:root {
  --bg:#ffffff;
  --ink:#160211; /* deep plum */
  --ink-80: rgba(22,2,17,.8);
  --card:#ffffff;
  --ring: rgba(255,255,255,.9);
  --shadow: 0 10px 30px rgba(22,2,17,.08);
}

/* ===== Stage ===== */
.stage {
  position: relative;
  min-height: 100dvh;
  overflow: hidden;
  display: grid;
  grid-template-rows: auto 1fr;
  isolation: isolate; /* ensure blurs sit behind content */
  font-family: "DM Sans", system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif;
  color: var(--ink);
  background: var(--bg);
  overflow-x: hidden;
}

/* Soft radial wash similar to the screenshot */
.wash {
  position: absolute; 
  inset: 0; 
  z-index: -3;
  background: radial-gradient(1200px 600px at 60% 80%, rgba(165, 142, 255, .35) 0%, rgba(255, 134, 225, .28) 28%, rgba(255,255,255, .9) 70%, #fff 100%);
}

/* Colored blurred blobs */
.blob { 
  position: absolute; 
  border-radius: 9999px; 
  filter: blur(var(--b, 140px)); 
  opacity: .9; 
  z-index: -2; 
}

.blob.pink { 
  width: 414px; 
  height: 414px; 
  left: 32%; 
  top: 58%; 
  background: #FF86E1; 
  --b: 250px; 
}

.blob.blue { 
  width: 280px; 
  height: 280px; 
  left: 55%; 
  top: 52%; 
  background: #89BCFF; 
  --b: 150px; 
}


