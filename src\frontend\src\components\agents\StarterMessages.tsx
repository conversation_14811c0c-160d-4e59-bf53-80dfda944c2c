import { ReactNode } from "react";
import { AgentIcon } from "./AgentIcon";
import styles from "./StarterMessages.module.css";

interface IStarterMessageProps {
  agentName?: string;
  agentLogo?: string;
  agentDescription?: string;
  onPromptClick?: (prompt: string) => void;
}

export function StarterMessages({
  agentName,
  agentLogo,
  agentDescription,
  onPromptClick,
}: IStarterMessageProps): ReactNode {
  // Default starter prompts for demonstration
  const defaultStarterPrompts = [
    "How can you help me?",
    "What are your capabilities?",
    "Tell me about yourself",
  ];

  return (
    <div className={styles.zeroprompt}>
      <div className={styles.content}>
        <AgentIcon
          alt={agentName ?? "Agent"}
          iconClassName={styles.emptyStateAgentIcon}
          iconName={agentLogo}
        />
        <h2 className={`${styles.welcome} text-subtitle1`}>
          {agentName ? `Hello! I'm ${agentName}` : "Hello! What can I help with?"}
        </h2>
        {agentDescription && (
          <p className={`${styles.caption} text-body1`}>{agentDescription}</p>
        )}
      </div>

      {onPromptClick && (
        <div className={styles.promptStarters}>
          {defaultStarterPrompts.map((prompt, index) => (
            <button
              key={`prompt-${index}`}
              className="btn-subtle"
              onClick={() => onPromptClick(prompt)}
            >
              <span className="text-body1">{prompt}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
