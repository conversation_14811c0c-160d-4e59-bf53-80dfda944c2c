import React, { useState, useEffect } from 'react';

interface ChatSession {
  id: string;
  session_id: string;
  title: string;
  created_at: string;
  updated_at: string;
  message_count: number;
}

interface ChatSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  onSessionSelect?: (sessionId: string) => void;
  onNewChat?: () => void;
  currentSessionId?: string | null;
  refreshTrigger?: number;
}

const ChatSidebar: React.FC<ChatSidebarProps> = ({
  isOpen,
  onToggle,
  onSessionSelect,
  onNewChat,
  currentSessionId,
  refreshTrigger
}) => {
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch chat sessions from API
  const fetchChatSessions = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/chat/sessions', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const sessions: ChatSession[] = await response.json();
        setChatSessions(sessions);
      } else {
        setError('Failed to load chat history');
      }
    } catch (err) {
      console.error('Error fetching chat sessions:', err);
      setError('Failed to load chat history');
    } finally {
      setIsLoading(false);
    }
  };

  // Load chat sessions when sidebar opens or refresh is triggered
  useEffect(() => {
    if (isOpen) {
      fetchChatSessions();
    }
  }, [isOpen, refreshTrigger]);

  // Group sessions by time period
  const groupSessionsByTime = (sessions: ChatSession[]) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const groups = {
      today: [] as ChatSession[],
      yesterday: [] as ChatSession[],
      thisWeek: [] as ChatSession[],
      older: [] as ChatSession[]
    };

    sessions.forEach(session => {
      const sessionDate = new Date(session.updated_at);

      if (sessionDate >= today) {
        groups.today.push(session);
      } else if (sessionDate >= yesterday) {
        groups.yesterday.push(session);
      } else if (sessionDate >= weekAgo) {
        groups.thisWeek.push(session);
      } else {
        groups.older.push(session);
      }
    });

    return groups;
  };

  const handleSessionClick = (sessionId: string) => {
    if (onSessionSelect) {
      onSessionSelect(sessionId);
    }
  };

  const handleNewChatClick = () => {
    if (onNewChat) {
      onNewChat();
    }
  };

  const handleDeleteSession = async (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent session selection

    if (confirm('Are you sure you want to delete this chat?')) {
      try {
        const response = await fetch(`/chat/sessions/${sessionId}`, {
          method: 'DELETE',
          credentials: 'include',
        });

        if (response.ok) {
          // Refresh the sessions list
          fetchChatSessions();
        } else {
          alert('Failed to delete chat session');
        }
      } catch (err) {
        console.error('Error deleting session:', err);
        alert('Failed to delete chat session');
      }
    }
  };

  const groupedSessions = groupSessionsByTime(chatSessions);

  const agents = [
    { name: "Agent A" },
    { name: "Agent B" },
    { name: "Agent C" }
  ];

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-25 z-40"
          onClick={onToggle}
        />
      )}
      
      {/* Sidebar */}
      <div
        className={`fixed top-0 left-0 h-full z-50 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        style={{
          width: isOpen ? '256px' : '80px',
          backgroundColor: 'white',
          boxShadow: '0px 0px 10px 5px rgba(0,0,0,0.10)'
        }}
      >
        {isOpen ? (
          // Open sidebar content
          <div className="relative w-64 h-full">
            {/* Header with icons */}
            <div className="absolute top-[30px] left-[30px]">
              <button onClick={onToggle} className="w-5 h-5">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" clipRule="evenodd" d="M3 2C2.44772 2 2 2.44772 2 3V17C2 17.5523 2.44772 18 3 18H17C17.5523 18 18 17.5523 18 17V3C18 2.44772 17.5523 2 17 2H3ZM0 3C0 1.34315 1.34315 0 3 0H17C18.6569 0 20 1.34315 20 3V17C20 18.6569 18.6569 20 17 20H3C1.34315 20 0 18.6569 0 17V3Z" fill="#160211"/>
                  <path fillRule="evenodd" clipRule="evenodd" d="M7 0C7.55228 0 8 0.447715 8 1V19C8 19.5523 7.55228 20 7 20C6.44772 20 6 19.5523 6 19V1C6 0.447715 6.44772 0 7 0Z" fill="#160211"/>
                </svg>
              </button>
            </div>
            
            <div className="absolute top-[30px] right-[30px]">
              <button className="w-5 h-5">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" clipRule="evenodd" d="M0.836984 1.78603C1.3729 1.25011 2.09975 0.949043 2.85764 0.949043H9.52548C10.0516 0.949043 10.478 1.37551 10.478 1.90159C10.478 2.42767 10.0516 2.85414 9.52548 2.85414H2.85764C2.60501 2.85414 2.36273 2.9545 2.18409 3.13313C2.00545 3.31177 1.9051 3.55405 1.9051 3.80669V17.1424C1.9051 17.395 2.00545 17.6373 2.18409 17.8159C2.36273 17.9945 2.60501 18.0949 2.85764 18.0949H16.1933C16.4459 18.0949 16.6882 17.9945 16.8669 17.8159C17.0455 17.6373 17.1459 17.395 17.1459 17.1424V10.4745C17.1459 9.94844 17.5723 9.52197 18.0984 9.52197C18.6245 9.52197 19.051 9.94844 19.051 10.4745V17.1424C19.051 17.9003 18.7499 18.6271 18.214 19.163C17.6781 19.6989 16.9512 20 16.1933 20H2.85764C2.09975 20 1.3729 19.6989 0.836984 19.163C0.301072 18.6271 0 17.9003 0 17.1424V3.80669C0 3.04879 0.301072 2.32194 0.836984 1.78603Z" fill="#160211"/>
                  <path fillRule="evenodd" clipRule="evenodd" d="M16.2715 2.21794L7.68625 10.8042C7.57326 10.917 7.48982 11.057 7.44477 11.2101L6.88899 13.111L8.79046 12.5551C8.94378 12.5103 9.08368 12.4276 9.19671 12.3148M9.19671 12.3148L17.782 3.72851C17.9823 3.5282 18.0949 3.25649 18.0949 2.97321C18.0949 2.68993 17.9824 2.41825 17.7821 2.21794C17.5818 2.01763 17.3101 1.9051 17.0268 1.9051C16.7435 1.9051 16.4718 2.01765 16.2715 2.21794M14.9244 0.870832C15.482 0.313248 16.2382 0 17.0268 0C17.8153 0 18.5716 0.313248 19.1292 0.870832C19.6868 1.42842 20 2.18466 20 2.97321C20 3.76173 19.6868 4.51796 19.1292 5.07554L10.5439 13.6618C10.2046 14.0008 9.78515 14.2493 9.32478 14.3837L6.58842 15.1837C6.34252 15.2555 6.08127 15.2599 5.83314 15.1964C5.58502 15.1328 5.35855 15.0037 5.17742 14.8226C4.9963 14.6415 4.8672 14.415 4.80363 14.1669C4.74006 13.9187 4.74436 13.6581 4.81608 13.4122L5.6167 10.6739C5.75173 10.214 6.00027 9.79543 6.33945 9.45676L14.9244 0.870832Z" fill="#160211"/>
                </svg>
              </button>
            </div>

            {/* Agents section */}
            <div className="absolute top-[90px] left-[30px] mb-4">
              {agents.map((agent, index) => (
                <div key={index} className="flex items-center mb-[30px]">
                  <div className="w-5 h-5 bg-stone-950 rounded-full relative">
                    <div className="w-3.5 h-3.5 bg-white absolute top-[3px] left-[3px]"></div>
                  </div>
                  <span className="ml-[30px] text-stone-950 text-sm font-normal font-['Manrope']">
                    {agent.name}
                  </span>
                </div>
              ))}
            </div>

            {/* New Chat Button */}
            <div className="absolute top-[250px] left-[30px] mb-4">
              <button
                onClick={handleNewChatClick}
                className="flex items-center w-48 p-2 text-sm font-normal font-['Manrope'] text-stone-950 hover:bg-gray-100 rounded-md transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                New Chat
              </button>
            </div>

            {/* Chat history */}
            <div className="absolute top-[290px] left-[30px] right-[30px] bottom-[80px] overflow-y-auto">
              {isLoading ? (
                <div className="text-slate-500 text-sm font-normal font-['Manrope']">
                  Loading chat history...
                </div>
              ) : error ? (
                <div className="text-red-500 text-sm font-normal font-['Manrope']">
                  {error}
                </div>
              ) : (
                <>
                  {groupedSessions.today.length > 0 && (
                    <div className="mb-6">
                      <div className="text-slate-500 text-sm font-normal font-['Manrope'] mb-3">
                        Today
                      </div>
                      {groupedSessions.today.map((session) => (
                        <div
                          key={session.session_id}
                          className={`group flex items-center justify-between text-stone-950 text-sm font-normal font-['Manrope'] mb-3 w-48 cursor-pointer hover:opacity-70 p-1 rounded ${
                            currentSessionId === session.session_id
                              ? 'bg-blue-100 border-l-2 border-blue-500'
                              : ''
                          }`}
                          onClick={() => handleSessionClick(session.session_id)}
                        >
                          <span className="truncate flex-1">{session.title}</span>
                          <button
                            onClick={(e) => handleDeleteSession(session.session_id, e)}
                            className="opacity-0 group-hover:opacity-100 ml-2 text-red-500 hover:text-red-700 transition-opacity"
                            title="Delete chat"
                          >
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {groupedSessions.yesterday.length > 0 && (
                    <div className="mb-6">
                      <div className="text-slate-500 text-sm font-normal font-['Manrope'] mb-3">
                        Yesterday
                      </div>
                      {groupedSessions.yesterday.map((session) => (
                        <div
                          key={session.session_id}
                          className={`group flex items-center justify-between text-stone-950 text-sm font-normal font-['Manrope'] mb-3 w-48 cursor-pointer hover:opacity-70 p-1 rounded ${
                            currentSessionId === session.session_id
                              ? 'bg-blue-100 border-l-2 border-blue-500'
                              : ''
                          }`}
                          onClick={() => handleSessionClick(session.session_id)}
                        >
                          <span className="truncate flex-1">{session.title}</span>
                          <button
                            onClick={(e) => handleDeleteSession(session.session_id, e)}
                            className="opacity-0 group-hover:opacity-100 ml-2 text-red-500 hover:text-red-700 transition-opacity"
                            title="Delete chat"
                          >
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {groupedSessions.thisWeek.length > 0 && (
                    <div className="mb-6">
                      <div className="text-slate-500 text-sm font-normal font-['Manrope'] mb-3">
                        This Week
                      </div>
                      {groupedSessions.thisWeek.map((session) => (
                        <div
                          key={session.session_id}
                          className={`group flex items-center justify-between text-stone-950 text-sm font-normal font-['Manrope'] mb-3 w-48 cursor-pointer hover:opacity-70 p-1 rounded ${
                            currentSessionId === session.session_id
                              ? 'bg-blue-100 border-l-2 border-blue-500'
                              : ''
                          }`}
                          onClick={() => handleSessionClick(session.session_id)}
                        >
                          <span className="truncate flex-1">{session.title}</span>
                          <button
                            onClick={(e) => handleDeleteSession(session.session_id, e)}
                            className="opacity-0 group-hover:opacity-100 ml-2 text-red-500 hover:text-red-700 transition-opacity"
                            title="Delete chat"
                          >
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {groupedSessions.older.length > 0 && (
                    <div className="mb-6">
                      <div className="text-slate-500 text-sm font-normal font-['Manrope'] mb-3">
                        Older
                      </div>
                      {groupedSessions.older.map((session) => (
                        <div
                          key={session.session_id}
                          className={`group flex items-center justify-between text-stone-950 text-sm font-normal font-['Manrope'] mb-3 w-48 cursor-pointer hover:opacity-70 p-1 rounded ${
                            currentSessionId === session.session_id
                              ? 'bg-blue-100 border-l-2 border-blue-500'
                              : ''
                          }`}
                          onClick={() => handleSessionClick(session.session_id)}
                        >
                          <span className="truncate flex-1">{session.title}</span>
                          <button
                            onClick={(e) => handleDeleteSession(session.session_id, e)}
                            className="opacity-0 group-hover:opacity-100 ml-2 text-red-500 hover:text-red-700 transition-opacity"
                            title="Delete chat"
                          >
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {chatSessions.length === 0 && !isLoading && (
                    <div className="text-slate-500 text-sm font-normal font-['Manrope']">
                      No chat history yet. Start a new conversation!
                    </div>
                  )}
                </>
              )}
            </div>

            {/* User profile */}
            <div className="absolute bottom-[32px] left-[25px] flex items-center">
              <img
                className="w-7 h-7 rounded-full"
                src="https://placehold.co/30x30"
                alt="User avatar"
              />
              <span className="ml-[18px] text-stone-950 text-sm font-normal font-['Manrope']">
                Vikash Chand
              </span>
            </div>
          </div>
        ) : (
          // Closed sidebar content
          <div className="relative w-20 h-full">
            {/* Toggle button */}
            <div className="absolute top-[30px] left-[30px]">
              <button onClick={onToggle} className="w-5 h-5">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" clipRule="evenodd" d="M3 2C2.44772 2 2 2.44772 2 3V17C2 17.5523 2.44772 18 3 18H17C17.5523 18 18 17.5523 18 17V3C18 2.44772 17.5523 2 17 2H3ZM0 3C0 1.34315 1.34315 0 3 0H17C18.6569 0 20 1.34315 20 3V17C20 18.6569 18.6569 20 17 20H3C1.34315 20 0 18.6569 0 17V3Z" fill="#160211"/>
                  <path fillRule="evenodd" clipRule="evenodd" d="M7 0C7.55228 0 8 0.447715 8 1V19C8 19.5523 7.55228 20 7 20C6.44772 20 6 19.5523 6 19V1C6 0.447715 6.44772 0 7 0Z" fill="#160211"/>
                </svg>
              </button>
            </div>

            {/* New chat button */}
            <div className="absolute top-[75px] left-[30px]">
              <button className="w-5 h-5">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" clipRule="evenodd" d="M0.836984 1.78603C1.3729 1.25011 2.09975 0.949043 2.85764 0.949043H9.52548C10.0516 0.949043 10.478 1.37551 10.478 1.90159C10.478 2.42767 10.0516 2.85414 9.52548 2.85414H2.85764C2.60501 2.85414 2.36273 2.9545 2.18409 3.13313C2.00545 3.31177 1.9051 3.55405 1.9051 3.80669V17.1424C1.9051 17.395 2.00545 17.6373 2.18409 17.8159C2.36273 17.9945 2.60501 18.0949 2.85764 18.0949H16.1933C16.4459 18.0949 16.6882 17.9945 16.8669 17.8159C17.0455 17.6373 17.1459 17.395 17.1459 17.1424V10.4745C17.1459 9.94844 17.5723 9.52197 18.0984 9.52197C18.6245 9.52197 19.051 9.94844 19.051 10.4745V17.1424C19.051 17.9003 18.7499 18.6271 18.214 19.163C17.6781 19.6989 16.9512 20 16.1933 20H2.85764C2.09975 20 1.3729 19.6989 0.836984 19.163C0.301072 18.6271 0 17.9003 0 17.1424V3.80669C0 3.04879 0.301072 2.32194 0.836984 1.78603Z" fill="#160211"/>
                  <path fillRule="evenodd" clipRule="evenodd" d="M16.2715 2.21794L7.68625 10.8042C7.57326 10.917 7.48982 11.057 7.44477 11.2101L6.88899 13.111L8.79046 12.5551C8.94378 12.5103 9.08368 12.4276 9.19671 12.3148M9.19671 12.3148L17.782 3.72851C17.9823 3.5282 18.0949 3.25649 18.0949 2.97321C18.0949 2.68993 17.9824 2.41825 17.7821 2.21794C17.5818 2.01763 17.3101 1.9051 17.0268 1.9051C16.7435 1.9051 16.4718 2.01765 16.2715 2.21794M14.9244 0.870832C15.482 0.313248 16.2382 0 17.0268 0C17.8153 0 18.5716 0.313248 19.1292 0.870832C19.6868 1.42842 20 2.18466 20 2.97321C20 3.76173 19.6868 4.51796 19.1292 5.07554L10.5439 13.6618C10.2046 14.0008 9.78515 14.2493 9.32478 14.3837L6.58842 15.1837C6.34252 15.2555 6.08127 15.2599 5.83314 15.1964C5.58502 15.1328 5.35855 15.0037 5.17742 14.8226C4.9963 14.6415 4.8672 14.415 4.80363 14.1669C4.74006 13.9187 4.74436 13.6581 4.81608 13.4122L5.6167 10.6739C5.75173 10.214 6.00027 9.79543 6.33945 9.45676L14.9244 0.870832Z" fill="#160211"/>
                </svg>
              </button>
            </div>

            {/* User avatar */}
            <div className="absolute bottom-[32px] left-[25px]">
              <img 
                className="w-7 h-7 rounded-full" 
                src="https://placehold.co/30x30" 
                alt="User avatar"
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ChatSidebar;
