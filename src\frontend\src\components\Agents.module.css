/* Main content container - centered vertically and horizontally */
.mainContent {
  text-align: center;
  margin-top: 25vh;
}

/* Header */
.header {
  margin-bottom: 10vh;
}

.sparkles {
  display: inline-block;
  margin-bottom: 25px;
}

.title {
  font-size: 26px;
  margin: 0;
  color: var(--ink, #160211);
}

/* Agent row - horizontal layout */
.agentRow {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 100px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .agentRow {
    gap: 40px;
  }
}

@media (max-width: 520px) {
  .agentRow {
    gap: 30px;
    flex-direction: column;
  }

  .mainContent {
    padding: 0 16px;
  }
}
