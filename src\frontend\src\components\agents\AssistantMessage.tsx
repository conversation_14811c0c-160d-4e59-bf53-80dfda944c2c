import { Trash2, ChevronDown, ChevronUp } from "lucide-react";
import { Suspense, useState } from "react";

import { Markdown } from "../core/Markdown";
import { UsageInfo } from "./UsageInfo";
import { IAssistantMessageProps } from "./chatbot/types";

import styles from "./AgentPreviewChatBot.module.css";
import { AgentIcon } from "./AgentIcon";



export function AssistantMessage({
  message,
  agentLogo,
  loadingState,
  agentName,
  showUsageInfo,
  onDelete,
}: IAssistantMessageProps): React.JSX.Element {
  const [showAllReferences, setShowAllReferences] = useState(false);
  const hasAnnotations = message.annotations && message.annotations.length > 0;
  const maxVisibleReferences = 3;

  const visibleReferences = hasAnnotations
    ? showAllReferences
      ? message.annotations
      : message.annotations?.slice(0, maxVisibleReferences)
    : [];

  const hiddenReferencesCount = hasAnnotations
    ? Math.max(0, (message.annotations?.length || 0) - maxVisibleReferences)
    : 0;

  return (
    <div
      id={"msg-" + message.id}
      className={`${styles.copilotChatMessage} flex gap-3 p-4 mb-4`}
    >
      {/* Avatar */}
      <div className="flex-shrink-0">
        <AgentIcon alt="" iconName={agentLogo} />
      </div>

      {/* Message Content */}
      <div className="flex-1 min-w-0">
        {/* Header with name and actions */}
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium" style={{ color: 'var(--colorNeutralForeground1)' }}>
            {agentName ?? "Bot"}
          </span>
          {onDelete && message.usageInfo && (
            <button
              className="btn-subtle p-1"
              onClick={() => {
                void onDelete(message.id);
              }}
              aria-label="Delete message"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          )}
        </div>

        {/* Message content */}
        <div
          className="mb-3"
          style={{
            padding: '10px',
            background: 'rgba(255, 255, 255, 0.50)',
            borderRadius: '8px',
            outline: '1px white solid',
            outlineOffset: '-1px',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '10px',
            display: 'inline-flex',
            width: '100%'
          }}
        >
          <div style={{ flex: '1 1 0' }}>
            {loadingState === "loading" ? (
              <div className="flex items-center gap-2">
                <div className="spinner spinner-small" />
                <span className="text-sm" style={{ color: '#160211' }}>
                  Thinking...
                </span>
              </div>
            ) : (
              <Suspense fallback={<div className="spinner spinner-small" />}>
                <Markdown content={message.content} />
              </Suspense>
            )}
          </div>
        </div>

        {/* References */}
        {hasAnnotations && (
          <div
            className="mb-3 rounded-fluent"
            style={{
              padding: '10px',
              background: 'rgba(255, 255, 255, 0.50)',
              borderRadius: '8px',
              outline: '1px white solid',
              outlineOffset: '-1px',
              display: 'inline-flex',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              width: '100%'
            }}
          >
            <div style={{ flex: '1 1 0' }}>
              {visibleReferences?.map((annotation, index) => (
                <span
                  key={index}
                  style={{
                    color: '#160211',
                    fontSize: '14px',
                    fontFamily: 'Manrope, sans-serif',
                    fontWeight: '400',
                    wordWrap: 'break-word'
                  }}
                >
                  {annotation.text || annotation.file_name}
                  {index < visibleReferences.length - 1 && <br />}
                </span>
              ))}
            </div>

            {/* Show more/less buttons */}
            {hasAnnotations && (message.annotations?.length || 0) > maxVisibleReferences && (
              <div className="mt-2">
                {!showAllReferences ? (
                  <button
                    className="btn-subtle text-sm flex items-center gap-1"
                    onClick={() => setShowAllReferences(true)}
                  >
                    <ChevronDown className="w-3 h-3" />
                    +{hiddenReferencesCount} more
                  </button>
                ) : (
                  <button
                    className="btn-subtle text-sm flex items-center gap-1"
                    onClick={() => setShowAllReferences(false)}
                  >
                    <ChevronUp className="w-3 h-3" />
                    Show Less
                  </button>
                )}
              </div>
            )}
          </div>
        )}

        {/* Usage Info */}
        {showUsageInfo && message.usageInfo && (
          <div className="mb-2">
            <UsageInfo info={message.usageInfo} duration={message.duration} />
          </div>
        )}
      </div>
    </div>
  );
}
