.feature {
  text-align: center;
}

.featureLink {
  display: inline-block;
  text-decoration: none;
  color: inherit;
  transition: transform 0.2s ease;
}

.featureLink:hover {
  transform: translateY(-2px);
}

.featureLink:focus-visible {
  outline: 2px solid var(--ink, #160211);
  outline-offset: 4px;
  border-radius: 10px;
}

.badge {
  width: 62px;
  height: 62px;
  margin: 0 auto 14px;
  display: grid;
  place-items: center;
  background: var(--ink, #160211);
  color: #fff;
  border-radius: 50%;
  transition: box-shadow 0.2s ease;
}

.featureLink:hover .badge {
  box-shadow: 0 4px 20px rgba(22, 2, 17, 0.2);
}

.inner {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: grid;
  place-items: center;
}

.label {
  font-size: 14px;
  color: var(--ink, #160211);
  font-weight: 500;
}

@media (prefers-reduced-motion: reduce) {
  .featureLink {
    transition: none;
  }
  
  .badge {
    transition: none;
  }
}
