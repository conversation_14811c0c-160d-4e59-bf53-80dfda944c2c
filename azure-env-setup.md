# Azure App Service Environment Variables Setup

## Required Environment Variables

Set these in your Azure App Service → Settings → Environment variables:

### Core Application Variables (UPDATED - Standardized Names)
```
AZURE_AI_PROJECT_ENDPOINT=https://lg-ai-aif-dev-res.services.ai.azure.com/api/projects/LG-AI-proj-dev
AZURE_AI_AGENT_ID=asst_lxGP6CVfgmk9suRmkMHdgavs
AZURE_AI_AGENT_NAME=Knowledge Assistant
AZURE_AI_AGENT_DEPLOYMENT_NAME=gpt-4o-mini
```

### Authentication (Choose Option A OR B)

#### Option A: Service Principal Authentication
```
AZURE_CLIENT_ID=your-service-principal-client-id
AZURE_CLIENT_SECRET=your-service-principal-secret  
AZURE_TENANT_ID=your-tenant-id
```

#### Option B: Managed Identity (Recommended)
1. Enable System-assigned managed identity in App Service
2. Grant the managed identity "Cognitive Services User" role on your AI Project
3. No additional environment variables needed

### Optional Variables
```
A<PERSON>URE_AI_SEARCH_CONNECTION_NAME=auelgaisearchrag
AZURE_AI_SEARCH_INDEX_NAME=rag-*************
AZURE_AI_SEARCH_ENDPOINT=https://auelgaisearchrag.search.windows.net
AZURE_AI_EMBED_DEPLOYMENT_NAME=text-embedding-3-large
AZURE_AI_EMBED_DIMENSIONS=256
AZURE_AI_PROJECT_RESOURCE_ID=/subscriptions/.../resourceGroups/.../providers/Microsoft.CognitiveServices/accounts/.../projects/...
ENABLE_AZURE_MONITOR_TRACING=false
AZURE_TRACING_GEN_AI_CONTENT_RECORDING_ENABLED=false
ENVIRONMENT=production
```

## Steps to Configure

1. **Azure Portal** → Your App Service
2. **Settings** → **Environment variables**
3. **Add** each variable above
4. **Save** changes
5. **Restart** the app service

## Verify Setup

After setting variables, check the logs to confirm authentication works:
- The error "DefaultAzureCredential failed" should disappear
- You should see "Created AIProjectClient" and "Agent already exists" messages
