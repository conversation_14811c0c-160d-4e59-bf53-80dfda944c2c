# Azure AI Project Configuration
# Infrastructure provides AZURE_EXISTING_* variables, fallback to AZURE_AI_* for local development
AZURE_EXISTING_AIPROJECT_RESOURCE_ID=
AZURE_EXISTING_AIPROJECT_ENDPOINT=
AZURE_EXISTING_AGENT_ID=
AZURE_AI_PROJECT_RESOURCE_ID=
AZURE_AI_PROJECT_ENDPOINT=
AZURE_AI_AGENT_ID=
AZURE_AI_AGENT_NAME=
AZURE_AI_AGENT_DEPLOYMENT_NAME=
AZURE_TENANT_ID=
AZURE_SUBSCRIPTION_ID=
AZURE_RESOURCE_GROUP=

# Azure AI Search Configuration (Optional)
AZURE_AI_SEARCH_CONNECTION_NAME=
AZURE_AI_SEARCH_INDEX_NAME=
AZURE_AI_SEARCH_ENDPOINT=
AZURE_AI_EMBED_DEPLOYMENT_NAME=
AZURE_AI_EMBED_DIMENSIONS=

# Monitoring Configuration (Optional)
ENABLE_AZURE_MONITOR_TRACING=false
AZURE_TRACING_GEN_AI_CONTENT_RECORDING_ENABLED=false

# Application Configuration
APP_LOG_FILE=

# Web Application Authentication (Optional)
# If not set, authentication will be disabled
WEB_APP_USERNAME=
WEB_APP_PASSWORD=

# Environment Configuration
ENVIRONMENT=development  # development, production

# CosmosDB Configuration for Chat History
COSMOS_DB_ENDPOINT=https://localhost:8081  # Local emulator for development
COSMOS_DB_DATABASE_NAME=chat_history
COSMOS_DB_CONTAINER_NAME=chat_data
