:root {
  --bg:#ffffff;
  --ink:#160211; /* deep plum */
  --ink-80: rgba(22,2,17,.8);
  --card:#ffffff;
  --ring: rgba(255,255,255,.9);
  --shadow: 0 10px 30px rgba(22,2,17,.08);
}

/* ===== Stage ===== */
.stage {
  position: relative;
  min-height: 100dvh;
  display: grid;
  grid-template-rows: auto 1fr;
  isolation: isolate; /* ensure blurs sit behind content */
  font-family: "DM Sans", system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif;
  color: var(--ink);
  background: var(--bg);
  overflow: hidden;
}

/* Soft radial wash similar to the screenshot */
.wash {
  position: absolute; 
  inset: 0; 
  z-index: -3;
  background: radial-gradient(1200px 600px at 60% 80%, rgba(165, 142, 255, .35) 0%, rgba(255, 134, 225, .28) 28%, rgba(255,255,255, .9) 70%, #fff 100%);
}

/* Colored blurred blobs */
.blob {
  position: absolute;
  border-radius: 9999px;
  filter: blur(var(--b, 140px));
  opacity: .9;
  z-index: -2;
  will-change: transform; /* Optimize for animations */
}

.blob.pink {
  width: 414px;
  height: 414px;
  background: #FF86E1;
  --b: 250px;
  animation: bouncePink 18s ease-in-out infinite;
}

.blob.blue {
  width: 280px;
  height: 280px;
  background: #89BCFF;
  --b: 150px;
  animation: bounceBlue 22s ease-in-out infinite;
}

/* Pink blob bouncing animation - figure-8 like pattern */
@keyframes bouncePink {
  0% {
    transform: translate(20vw, 60vh);
  }
  12.5% {
    transform: translate(70vw, 20vh);
  }
  25% {
    transform: translate(80vw, 70vh);
  }
  37.5% {
    transform: translate(60vw, 80vh);
  }
  50% {
    transform: translate(10vw, 75vh);
  }
  62.5% {
    transform: translate(5vw, 30vh);
  }
  75% {
    transform: translate(40vw, 10vh);
  }
  87.5% {
    transform: translate(75vw, 45vh);
  }
  100% {
    transform: translate(20vw, 60vh);
  }
}

/* Blue blob bouncing animation - different path */
@keyframes bounceBlue {
  0% {
    transform: translate(50vw, 40vh);
  }
  11% {
    transform: translate(15vw, 15vh);
  }
  22% {
    transform: translate(5vw, 65vh);
  }
  33% {
    transform: translate(35vw, 85vh);
  }
  44% {
    transform: translate(70vw, 75vh);
  }
  55% {
    transform: translate(85vw, 35vh);
  }
  66% {
    transform: translate(75vw, 5vh);
  }
  77% {
    transform: translate(45vw, 25vh);
  }
  88% {
    transform: translate(25vw, 55vh);
  }
  100% {
    transform: translate(50vw, 40vh);
  }
}
