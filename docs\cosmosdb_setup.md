# CosmosDB Setup

Quick setup for chat history persistence.

## Environment Variables

Add to your `.env` file:

```bash
COSMOS_DB_ENDPOINT=https://your-cosmosdb-account.documents.azure.com:443/
COSMOS_DB_DATABASE_NAME=chat_history
COSMOS_DB_CONTAINER_NAME=chat_data
```

## Azure Setup

1. Create CosmosDB account (Core SQL API)
2. For local dev: `az login`
3. Give your identity `DocumentDB Account Contributor` role

## Database Schema

Auto-created:
- Database: `chat_history`
- Container: `chat_data` (partition key: `/session_id`)

Session doc:
```json
{
  "session_id": "uuid",
  "user_id": "default_user",
  "title": "Smart title from first message",
  "type": "session"
}
```

Message doc:
```json
{
  "session_id": "uuid",
  "role": "user|assistant",
  "content": "text",
  "type": "message"
}
```

## What You Get

- Sidebar shows chat history (Today/Yesterday/This Week/Older)
- Smart titles from first message
- Sessions only created when you actually send a message
- URL updates to `/chat/{session_id}` automatically

## Troubleshooting

Not working? Check:
1. Logs for CosmosDB errors
2. Endpoint URL is correct
3. Identity has permissions
4. `az login` for local dev

## Notes

- Uses 400 RU/s by default
- Free tier: 1000 RU/s, 25 GB
- Data stored forever (add TTL if needed)
