{"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "18114037235110680016"}}, "parameters": {"environmentName": {"type": "string", "minLength": 1, "maxLength": 64, "metadata": {"description": "Name of the the environment which is used to generate a short unique hash used in all resources."}}, "location": {"type": "string", "allowedValues": ["eastus", "eastus2", "swedencentral", "west<PERSON>", "westus3"], "metadata": {"azd": {"type": "location", "usageName": ["OpenAI.GlobalStandard.gpt-4o-mini,30", "OpenAI.GlobalStandard.text-embedding-3-small,30"]}, "description": "Location for all resources"}}, "aiExistingProjectConnectionString": {"type": "string", "defaultValue": "", "metadata": {"description": "Use this parameter to use an existing AI project connection string"}}, "resourceGroupName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Azure resource group where new resources will be deployed"}}, "aiHubName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Azure AI Foundry Hub resource name. If ommited will be generated"}}, "aiProjectName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Azure AI Foundry project name. If ommited will be generated"}}, "applicationInsightsName": {"type": "string", "defaultValue": "", "metadata": {"description": "The application insights resource name. If ommited will be generated"}}, "aiServicesName": {"type": "string", "defaultValue": "", "metadata": {"description": "The AI Services resource name. If ommited will be generated"}}, "aiServicesConnectionName": {"type": "string", "defaultValue": "", "metadata": {"description": "The AI Services connection name. If ommited will use a default value"}}, "keyVaultName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Azure Key Vault resource name. If ommited will be generated"}}, "searchServiceName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Azure Search resource name. If ommited will be generated"}}, "searchConnectionName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Azure Search connection name. If ommited will use a default value"}}, "aiSearchIndexName": {"type": "string", "defaultValue": "", "metadata": {"description": "The search index name"}}, "storageAccountName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Azure Storage Account resource name. If ommited will be generated"}}, "logAnalyticsWorkspaceName": {"type": "string", "defaultValue": "", "metadata": {"description": "The log analytics workspace name. If ommited will be generated"}}, "agentModelFormat": {"type": "string", "defaultValue": "OpenAI", "allowedValues": ["Microsoft", "OpenAI"], "metadata": {"description": "Format of the chat model to deploy"}}, "agentName": {"type": "string", "defaultValue": "agent-template-assistant", "metadata": {"description": "Name of agent to deploy"}}, "aiAgentID": {"type": "string", "defaultValue": "", "metadata": {"description": "(Deprecated) ID of agent to deploy"}}, "azureExistingAgentId": {"type": "string", "defaultValue": "", "metadata": {"description": "ID of the existing agent"}}, "agentModelName": {"type": "string", "defaultValue": "gpt-4o-mini", "metadata": {"description": "Name of the chat model to deploy"}}, "agentDeploymentName": {"type": "string", "defaultValue": "gpt-4o-mini", "metadata": {"description": "Name of the model deployment"}}, "agentModelVersion": {"type": "string", "defaultValue": "2024-07-18", "metadata": {"description": "Version of the chat model to deploy"}}, "agentDeploymentSku": {"type": "string", "defaultValue": "GlobalStandard", "metadata": {"description": "<PERSON><PERSON> of the chat deployment"}}, "agentDeploymentCapacity": {"type": "int", "defaultValue": 30, "metadata": {"description": "Capacity of the chat deployment"}}, "embedModelFormat": {"type": "string", "defaultValue": "OpenAI", "allowedValues": ["Microsoft", "OpenAI"], "metadata": {"description": "Format of the embedding model to deploy"}}, "embedModelName": {"type": "string", "defaultValue": "text-embedding-3-small", "metadata": {"description": "Name of the embedding model to deploy"}}, "embeddingDeploymentName": {"type": "string", "defaultValue": "text-embedding-3-small", "metadata": {"description": "Name of the embedding model deployment"}}, "embeddingDeploymentDimensions": {"type": "string", "defaultValue": "100", "metadata": {"description": "Embedding model dimensionality"}}, "embedModelVersion": {"type": "securestring", "defaultValue": "1", "metadata": {"description": "Version of the embedding model to deploy"}}, "embedDeploymentSku": {"type": "string", "defaultValue": "Standard", "metadata": {"description": "Sku of the embeddings model deployment"}}, "embedDeploymentCapacity": {"type": "int", "defaultValue": 30, "metadata": {"description": "Capacity of the embedding deployment"}}, "useApplicationInsights": {"type": "bool", "defaultValue": true}, "useSearchService": {"type": "bool", "defaultValue": false, "metadata": {"description": "Do we want to use the Azure AI Search"}}, "seed": {"type": "string", "defaultValue": "[newGuid()]", "metadata": {"description": "Random seed to be used during generation of new resources suffixes."}}}, "variables": {"$fxv#0": {"analysisServicesServers": "as", "apiManagementService": "apim-", "appConfigurationStores": "appcs-", "appManagedEnvironments": "cae-", "appContainerApps": "ca-", "authorizationPolicyDefinitions": "policy-", "automationAutomationAccounts": "aa-", "blueprintBlueprints": "bp-", "blueprintBlueprintsArtifacts": "bpa-", "cacheRedis": "redis-", "cdnProfiles": "cdnp-", "cdnProfilesEndpoints": "cdne-", "cognitiveServicesAccounts": "cog-", "cognitiveServicesFormRecognizer": "cog-fr-", "cognitiveServicesTextAnalytics": "cog-ta-", "computeAvailabilitySets": "avail-", "computeCloudServices": "cld-", "computeDiskEncryptionSets": "des", "computeDisks": "disk", "computeDisksOs": "osdisk", "computeGalleries": "gal", "computeSnapshots": "snap-", "computeVirtualMachines": "vm", "computeVirtualMachineScaleSets": "vmss-", "containerInstanceContainerGroups": "ci", "containerRegistryRegistries": "cr", "containerServiceManagedClusters": "aks-", "databricksWorkspaces": "dbw-", "dataFactoryFactories": "adf-", "dataLakeAnalyticsAccounts": "dla", "dataLakeStoreAccounts": "dls", "dataMigrationServices": "dms-", "dBforMySQLServers": "mysql-", "dBforPostgreSQLServers": "psql-", "devicesIotHubs": "iot-", "devicesProvisioningServices": "provs-", "devicesProvisioningServicesCertificates": "pcert-", "documentDBDatabaseAccounts": "cosmos-", "eventGridDomains": "evgd-", "eventGridDomainsTopics": "evgt-", "eventGridEventSubscriptions": "evgs-", "eventHubNamespaces": "evhns-", "eventHubNamespacesEventHubs": "evh-", "hdInsightClustersHadoop": "hadoop-", "hdInsightClustersHbase": "hbase-", "hdInsightClustersKafka": "kafka-", "hdInsightClustersMl": "mls-", "hdInsightClustersSpark": "spark-", "hdInsightClustersStorm": "storm-", "hybridComputeMachines": "arcs-", "insightsActionGroups": "ag-", "insightsComponents": "appi-", "keyVaultVaults": "kv-", "kubernetesConnectedClusters": "arck", "kustoClusters": "dec", "kustoClustersDatabases": "dedb", "loadTesting": "lt-", "logicIntegrationAccounts": "ia-", "logicWorkflows": "logic-", "machineLearningServicesWorkspaces": "mlw-", "managedIdentityUserAssignedIdentities": "id-", "managementManagementGroups": "mg-", "migrateAssessmentProjects": "migr-", "networkApplicationGateways": "agw-", "networkApplicationSecurityGroups": "asg-", "networkAzureFirewalls": "afw-", "networkBastionHosts": "bas-", "networkConnections": "con-", "networkDnsZones": "dnsz-", "networkExpressRouteCircuits": "erc-", "networkFirewallPolicies": "afwp-", "networkFirewallPoliciesWebApplication": "waf", "networkFirewallPoliciesRuleGroups": "wafrg", "networkFrontDoors": "fd-", "networkFrontdoorWebApplicationFirewallPolicies": "fdfp-", "networkLoadBalancersExternal": "lbe-", "networkLoadBalancersInternal": "lbi-", "networkLoadBalancersInboundNatRules": "rule-", "networkLocalNetworkGateways": "lgw-", "networkNatGateways": "ng-", "networkNetworkInterfaces": "nic-", "networkNetworkSecurityGroups": "nsg-", "networkNetworkSecurityGroupsSecurityRules": "nsgsr-", "networkNetworkWatchers": "nw-", "networkPrivateDnsZones": "pdnsz-", "networkPrivateLinkServices": "pl-", "networkPublicIPAddresses": "pip-", "networkPublicIPPrefixes": "ippre-", "networkRouteFilters": "rf-", "networkRouteTables": "rt-", "networkRouteTablesRoutes": "udr-", "networkTrafficManagerProfiles": "traf-", "networkVirtualNetworkGateways": "vgw-", "networkVirtualNetworks": "vnet-", "networkVirtualNetworksSubnets": "snet-", "networkVirtualNetworksVirtualNetworkPeerings": "peer-", "networkVirtualWans": "vwan-", "networkVpnGateways": "vpng-", "networkVpnGatewaysVpnConnections": "vcn-", "networkVpnGatewaysVpnSites": "vst-", "notificationHubsNamespaces": "ntfns-", "notificationHubsNamespacesNotificationHubs": "ntf-", "operationalInsightsWorkspaces": "log-", "portalDashboards": "dash-", "powerBIDedicatedCapacities": "pbi-", "purviewAccounts": "pview-", "recoveryServicesVaults": "rsv-", "resourcesResourceGroups": "rg-", "searchSearchServices": "srch-", "serviceBusNamespaces": "sb-", "serviceBusNamespacesQueues": "sbq-", "serviceBusNamespacesTopics": "sbt-", "serviceEndPointPolicies": "se-", "serviceFabricClusters": "sf-", "signalRServiceSignalR": "sigr", "sqlManagedInstances": "sqlmi-", "sqlServers": "sql-", "sqlServersDataWarehouse": "sqldw-", "sqlServersDatabases": "sqldb-", "sqlServersDatabasesStretch": "sqlstrdb-", "storageStorageAccounts": "st", "storageStorageAccountsVm": "stvm", "storSimpleManagers": "ssimp", "streamAnalyticsCluster": "asa-", "synapseWorkspaces": "syn", "synapseWorkspacesAnalyticsWorkspaces": "synw", "synapseWorkspacesSqlPoolsDedicated": "syndp", "synapseWorkspacesSqlPoolsSpark": "synsp", "timeSeriesInsightsEnvironments": "tsi-", "webServerFarms": "plan-", "webSitesAppService": "app-", "webSitesAppServiceEnvironment": "ase-", "webSitesFunctions": "func-", "webStaticSites": "stapp-"}, "abbrs": "[variables('$fxv#0')]", "resourceToken": "[toLower(uniqueString(subscription().id, parameters('environmentName'), parameters('location'), parameters('seed')))]", "projectName": "[if(not(empty(parameters('aiProjectName'))), parameters('aiProjectName'), format('ai-project-{0}', variables('resourceToken')))]", "tags": {"azd-env-name": "[parameters('environmentName')]"}, "tempAgentID": "[if(not(empty(parameters('aiAgentID'))), parameters('aiAgentID'), '')]", "agentID": "[if(not(empty(parameters('azureExistingAgentId'))), parameters('azureExistingAgentId'), variables('tempAgentID'))]", "aiChatModel": [{"name": "[parameters('agentDeploymentName')]", "model": {"format": "[parameters('agentModelFormat')]", "name": "[parameters('agentModelName')]", "version": "[parameters('agentModelVersion')]"}, "sku": {"name": "[parameters('agentDeploymentSku')]", "capacity": "[parameters('agentDeploymentCapacity')]"}}], "aiEmbeddingModel": [{"name": "[parameters('embeddingDeploymentName')]", "model": {"format": "[parameters('embedModelFormat')]", "name": "[parameters('embedModelName')]", "version": "[parameters('embedModelVersion')]"}, "sku": {"name": "[parameters('embedDeploymentSku')]", "capacity": "[parameters('embedDeploymentCapacity')]"}}], "aiDeployments": "[concat(variables('aiChatModel'), if(parameters('useSearchService'), variables('aiEmbeddingModel'), createArray()))]", "logAnalyticsWorkspaceResolvedName": "[if(not(parameters('useApplicationInsights')), '', if(not(empty(parameters('logAnalyticsWorkspaceName'))), parameters('logAnalyticsWorkspaceName'), format('{0}{1}', variables('abbrs').operationalInsightsWorkspaces, variables('resourceToken'))))]", "resolvedSearchServiceName": "[if(not(parameters('useSearchService')), '', if(not(empty(parameters('searchServiceName'))), parameters('searchServiceName'), format('{0}{1}', variables('abbrs').searchSearchServices, variables('resourceToken'))))]", "resolvedApplicationInsightsName": "[if(or(not(parameters('useApplicationInsights')), not(empty(parameters('aiExistingProjectConnectionString')))), '', if(not(empty(parameters('applicationInsightsName'))), parameters('applicationInsightsName'), format('{0}{1}', variables('abbrs').insightsComponents, variables('resourceToken'))))]"}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "apiVersion": "2021-04-01", "name": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "location": "[parameters('location')]", "tags": "[variables('tags')]"}, {"condition": "[empty(parameters('aiExistingProjectConnectionString'))]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "ai", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[variables('tags')]"}, "hubName": "[if(not(empty(parameters('aiHubName'))), createObject('value', parameters('aiHubName')), createObject('value', format('ai-hub-{0}', variables('resourceToken'))))]", "projectName": {"value": "[variables('projectName')]"}, "keyVaultName": "[if(not(empty(parameters('keyVaultName'))), createObject('value', parameters('keyVaultName')), createObject('value', format('{0}{1}', variables('abbrs').keyVaultVaults, variables('resourceToken'))))]", "storageAccountName": "[if(not(empty(parameters('storageAccountName'))), createObject('value', parameters('storageAccountName')), createObject('value', format('{0}{1}', variables('abbrs').storageStorageAccounts, variables('resourceToken'))))]", "aiServicesName": "[if(not(empty(parameters('aiServicesName'))), createObject('value', parameters('aiServicesName')), createObject('value', format('aoai-{0}', variables('resourceToken'))))]", "aiServicesConnectionName": "[if(not(empty(parameters('aiServicesConnectionName'))), createObject('value', parameters('aiServicesConnectionName')), createObject('value', format('aoai-{0}', variables('resourceToken'))))]", "aiServiceModelDeployments": {"value": "[variables('aiDeployments')]"}, "logAnalyticsName": {"value": "[variables('logAnalyticsWorkspaceResolvedName')]"}, "applicationInsightsName": "[if(not(parameters('useApplicationInsights')), createObject('value', ''), if(not(empty(parameters('applicationInsightsName'))), createObject('value', parameters('applicationInsightsName')), createObject('value', format('{0}{1}', variables('abbrs').insightsComponents, variables('resourceToken')))))]", "searchServiceName": {"value": "[variables('resolvedSearchServiceName')]"}, "searchConnectionName": "[if(not(parameters('useSearchService')), createObject('value', ''), if(not(empty(parameters('searchConnectionName'))), createObject('value', parameters('searchConnectionName')), createObject('value', 'search-service-connection')))]"}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "691137933995863928"}}, "parameters": {"location": {"type": "string", "minLength": 1, "metadata": {"description": "Primary location for all resources"}}, "hubName": {"type": "string", "metadata": {"description": "The AI Hub resource name."}}, "projectName": {"type": "string", "metadata": {"description": "The AI Project resource name."}}, "keyVaultName": {"type": "string", "metadata": {"description": "The Key Vault resource name."}}, "storageAccountName": {"type": "string", "metadata": {"description": "The Storage Account resource name."}}, "aiServicesName": {"type": "string", "metadata": {"description": "The AI Services resource name."}}, "aiServicesConnectionName": {"type": "string", "metadata": {"description": "The AI Services connection name."}}, "aiServiceModelDeployments": {"type": "array", "defaultValue": [], "metadata": {"description": "The AI Services model deployments."}}, "logAnalyticsName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Log Analytics resource name."}}, "applicationInsightsName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Application Insights resource name."}}, "containerRegistryName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Container Registry resource name."}}, "searchServiceName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Azure Search resource name."}}, "searchConnectionName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Azure Search connection name."}}, "tags": {"type": "object", "defaultValue": {}}}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "hubDependencies", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "keyVaultName": {"value": "[parameters('keyVaultName')]"}, "storageAccountName": {"value": "[parameters('storageAccountName')]"}, "applicationInsightsName": {"value": "[parameters('applicationInsightsName')]"}, "logAnalyticsName": {"value": "[parameters('logAnalyticsName')]"}, "aiServicesName": {"value": "[parameters('aiServicesName')]"}, "aiServiceModelDeployments": {"value": "[parameters('aiServiceModelDeployments')]"}, "searchServiceName": {"value": "[parameters('searchServiceName')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "618022947787971888"}}, "parameters": {"location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "keyVaultName": {"type": "string", "metadata": {"description": "Name of the key vault"}}, "storageAccountName": {"type": "string", "metadata": {"description": "Name of the storage account"}}, "aiServicesName": {"type": "string", "metadata": {"description": "Name of the AI Service"}}, "aiServiceModelDeployments": {"type": "array", "defaultValue": [], "metadata": {"description": "Array of OpenAI model deployments"}}, "logAnalyticsName": {"type": "string", "defaultValue": "", "metadata": {"description": "Name of the Log Analytics workspace"}}, "applicationInsightsName": {"type": "string", "defaultValue": "", "metadata": {"description": "Name of the Application Insights instance"}}, "searchServiceName": {"type": "string", "defaultValue": "", "metadata": {"description": "Name of the Azure Cognitive Search service"}}}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "keyvault", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "name": {"value": "[parameters('keyVaultName')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "8365666898075252029"}, "description": "Creates an Azure Key Vault."}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "principalId": {"type": "string", "defaultValue": ""}}, "resources": [{"type": "Microsoft.KeyVault/vaults", "apiVersion": "2022-07-01", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "properties": {"tenantId": "[subscription().tenantId]", "sku": {"family": "A", "name": "standard"}, "accessPolicies": "[if(not(empty(parameters('principalId'))), createArray(createObject('objectId', parameters('principalId'), 'permissions', createObject('secrets', createArray('get', 'list')), 'tenantId', subscription().tenantId)), createArray())]"}}], "outputs": {"endpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.KeyVault/vaults', parameters('name')), '2022-07-01').vaultUri]"}, "id": {"type": "string", "value": "[resourceId('Microsoft.KeyVault/vaults', parameters('name'))]"}, "name": {"type": "string", "value": "[parameters('name')]"}}}}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "storageAccount", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "name": {"value": "[parameters('storageAccountName')]"}, "containers": {"value": [{"name": "default"}]}, "files": {"value": [{"name": "default"}]}, "queues": {"value": [{"name": "default"}]}, "tables": {"value": [{"name": "default"}]}, "corsRules": {"value": [{"allowedOrigins": ["https://mlworkspace.azure.ai", "https://ml.azure.com", "https://*.ml.azure.com", "https://ai.azure.com", "https://*.ai.azure.com", "https://mlworkspacecanary.azure.ai", "https://mlworkspace.azureml-test.net"], "allowedMethods": ["GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "maxAgeInSeconds": 1800, "exposedHeaders": ["*"], "allowedHeaders": ["*"]}]}, "deleteRetentionPolicy": {"value": {"allowPermanentDelete": false, "enabled": false}}, "shareDeleteRetentionPolicy": {"value": {"enabled": true, "days": 7}}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "13434473786895597507"}, "description": "Creates an Azure storage account."}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "accessTier": {"type": "string", "defaultValue": "Hot", "allowedValues": ["Cool", "Hot", "Premium"]}, "allowBlobPublicAccess": {"type": "bool", "defaultValue": true}, "allowCrossTenantReplication": {"type": "bool", "defaultValue": true}, "allowSharedKeyAccess": {"type": "bool", "defaultValue": true}, "containers": {"type": "array", "defaultValue": []}, "corsRules": {"type": "array", "defaultValue": []}, "defaultToOAuthAuthentication": {"type": "bool", "defaultValue": false}, "deleteRetentionPolicy": {"type": "object", "defaultValue": {}}, "dnsEndpointType": {"type": "string", "defaultValue": "Standard", "allowedValues": ["AzureDnsZone", "Standard"]}, "files": {"type": "array", "defaultValue": []}, "kind": {"type": "string", "defaultValue": "StorageV2"}, "minimumTlsVersion": {"type": "string", "defaultValue": "TLS1_2"}, "queues": {"type": "array", "defaultValue": []}, "shareDeleteRetentionPolicy": {"type": "object", "defaultValue": {}}, "supportsHttpsTrafficOnly": {"type": "bool", "defaultValue": true}, "tables": {"type": "array", "defaultValue": []}, "networkAcls": {"type": "object", "defaultValue": {"bypass": "AzureServices", "defaultAction": "Allow"}}, "publicNetworkAccess": {"type": "string", "defaultValue": "Enabled", "allowedValues": ["Enabled", "Disabled"]}, "sku": {"type": "object", "defaultValue": {"name": "Standard_LRS"}}}, "resources": [{"copy": {"name": "storage::blobServices::container", "count": "[length(parameters('containers'))]"}, "condition": "[not(empty(parameters('containers')))]", "type": "Microsoft.Storage/storageAccounts/blobServices/containers", "apiVersion": "2023-01-01", "name": "[format('{0}/{1}/{2}', parameters('name'), 'default', parameters('containers')[copyIndex()].name)]", "properties": {"publicAccess": "[if(contains(parameters('containers')[copyIndex()], 'publicAccess'), parameters('containers')[copyIndex()].publicAccess, 'None')]"}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts/blobServices', parameters('name'), 'default')]"]}, {"copy": {"name": "storage::queueServices::queue", "count": "[length(parameters('queues'))]"}, "condition": "[not(empty(parameters('queues')))]", "type": "Microsoft.Storage/storageAccounts/queueServices/queues", "apiVersion": "2023-01-01", "name": "[format('{0}/{1}/{2}', parameters('name'), 'default', parameters('queues')[copyIndex()].name)]", "properties": {"metadata": {}}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts/queueServices', parameters('name'), 'default')]"]}, {"condition": "[not(empty(parameters('containers')))]", "type": "Microsoft.Storage/storageAccounts/blobServices", "apiVersion": "2023-01-01", "name": "[format('{0}/{1}', parameters('name'), 'default')]", "properties": {"cors": {"corsRules": "[parameters('corsRules')]"}, "deleteRetentionPolicy": "[parameters('deleteRetentionPolicy')]"}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', parameters('name'))]"]}, {"condition": "[not(empty(parameters('files')))]", "type": "Microsoft.Storage/storageAccounts/fileServices", "apiVersion": "2023-01-01", "name": "[format('{0}/{1}', parameters('name'), 'default')]", "properties": {"cors": {"corsRules": "[parameters('corsRules')]"}, "shareDeleteRetentionPolicy": "[parameters('shareDeleteRetentionPolicy')]"}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', parameters('name'))]"]}, {"condition": "[not(empty(parameters('queues')))]", "type": "Microsoft.Storage/storageAccounts/queueServices", "apiVersion": "2023-01-01", "name": "[format('{0}/{1}', parameters('name'), 'default')]", "properties": {}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', parameters('name'))]"]}, {"condition": "[not(empty(parameters('tables')))]", "type": "Microsoft.Storage/storageAccounts/tableServices", "apiVersion": "2023-01-01", "name": "[format('{0}/{1}', parameters('name'), 'default')]", "properties": {}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', parameters('name'))]"]}, {"type": "Microsoft.Storage/storageAccounts", "apiVersion": "2023-01-01", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "kind": "[parameters('kind')]", "sku": "[parameters('sku')]", "properties": {"accessTier": "[parameters('accessTier')]", "allowBlobPublicAccess": "[parameters('allowBlobPublicAccess')]", "allowCrossTenantReplication": "[parameters('allowCrossTenantReplication')]", "allowSharedKeyAccess": "[parameters('allowSharedKeyAccess')]", "defaultToOAuthAuthentication": "[parameters('defaultToOAuthAuthentication')]", "dnsEndpointType": "[parameters('dnsEndpointType')]", "minimumTlsVersion": "[parameters('minimumTlsVersion')]", "networkAcls": "[parameters('networkAcls')]", "publicNetworkAccess": "[parameters('publicNetworkAccess')]", "supportsHttpsTrafficOnly": "[parameters('supportsHttpsTrafficOnly')]"}}], "outputs": {"id": {"type": "string", "value": "[resourceId('Microsoft.Storage/storageAccounts', parameters('name'))]"}, "name": {"type": "string", "value": "[parameters('name')]"}, "primaryEndpoints": {"type": "object", "value": "[reference(resourceId('Microsoft.Storage/storageAccounts', parameters('name')), '2023-01-01').primaryEndpoints]"}}}}}, {"condition": "[not(empty(parameters('logAnalyticsName')))]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "logAnalytics", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "name": {"value": "[parameters('logAnalyticsName')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "*****************"}, "description": "Creates a Log Analytics workspace."}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}}, "resources": [{"type": "Microsoft.OperationalInsights/workspaces", "apiVersion": "2021-12-01-preview", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "properties": {"retentionInDays": 30, "features": {"searchVersion": 1}, "sku": {"name": "PerGB2018"}}}], "outputs": {"id": {"type": "string", "value": "[resourceId('Microsoft.OperationalInsights/workspaces', parameters('name'))]"}, "name": {"type": "string", "value": "[parameters('name')]"}}}}}, {"condition": "[and(not(empty(parameters('applicationInsightsName'))), not(empty(parameters('logAnalyticsName'))))]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "applicationInsights", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "name": {"value": "[parameters('applicationInsightsName')]"}, "logAnalyticsWorkspaceId": "[if(not(empty(parameters('logAnalyticsName'))), createObject('value', reference(resourceId('Microsoft.Resources/deployments', 'logAnalytics'), '2022-09-01').outputs.id.value), createObject('value', ''))]"}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "14508427667657106623"}, "description": "Creates an Application Insights instance based on an existing Log Analytics workspace."}, "parameters": {"name": {"type": "string"}, "dashboardName": {"type": "string", "defaultValue": ""}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "logAnalyticsWorkspaceId": {"type": "string"}}, "resources": [{"type": "Microsoft.Insights/components", "apiVersion": "2020-02-02", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "kind": "web", "properties": {"Application_Type": "web", "WorkspaceResourceId": "[parameters('logAnalyticsWorkspaceId')]"}}, {"condition": "[not(empty(parameters('dashboardName')))]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "application-insights-dashboard", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"name": {"value": "[parameters('dashboardName')]"}, "location": {"value": "[parameters('location')]"}, "applicationInsightsName": {"value": "[parameters('name')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "14424091050138344443"}, "description": "Creates a dashboard for an Application Insights instance."}, "parameters": {"name": {"type": "string"}, "applicationInsightsName": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}}, "resources": [{"type": "Microsoft.Portal/dashboards", "apiVersion": "2020-09-01-preview", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "properties": {"lenses": [{"order": 0, "parts": [{"position": {"x": 0, "y": 0, "colSpan": 2, "rowSpan": 1}, "metadata": {"inputs": [{"name": "id", "value": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, {"name": "Version", "value": "1.0"}], "type": "Extension/AppInsightsExtension/PartType/AspNetOverviewPinnedPart", "asset": {"idInputName": "id", "type": "ApplicationInsights"}, "defaultMenuItemId": "overview"}}, {"position": {"x": 2, "y": 0, "colSpan": 1, "rowSpan": 1}, "metadata": {"inputs": [{"name": "ComponentId", "value": {"Name": "[parameters('applicationInsightsName')]", "SubscriptionId": "[subscription().subscriptionId]", "ResourceGroup": "[resourceGroup().name]"}}, {"name": "Version", "value": "1.0"}], "type": "Extension/AppInsightsExtension/PartType/ProactiveDetectionAsyncPart", "asset": {"idInputName": "ComponentId", "type": "ApplicationInsights"}, "defaultMenuItemId": "ProactiveDetection"}}, {"position": {"x": 3, "y": 0, "colSpan": 1, "rowSpan": 1}, "metadata": {"inputs": [{"name": "ComponentId", "value": {"Name": "[parameters('applicationInsightsName')]", "SubscriptionId": "[subscription().subscriptionId]", "ResourceGroup": "[resourceGroup().name]"}}, {"name": "ResourceId", "value": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}], "type": "Extension/AppInsightsExtension/PartType/QuickPulseButtonSmallPart", "asset": {"idInputName": "ComponentId", "type": "ApplicationInsights"}}}, {"position": {"x": 4, "y": 0, "colSpan": 1, "rowSpan": 1}, "metadata": {"inputs": [{"name": "ComponentId", "value": {"Name": "[parameters('applicationInsightsName')]", "SubscriptionId": "[subscription().subscriptionId]", "ResourceGroup": "[resourceGroup().name]"}}, {"name": "TimeContext", "value": {"durationMs": 86400000, "endTime": null, "createdTime": "2018-05-04T01:20:33.345Z", "isInitialTime": true, "grain": 1, "useDashboardTimeRange": false}}, {"name": "Version", "value": "1.0"}], "type": "Extension/AppInsightsExtension/PartType/AvailabilityNavButtonPart", "asset": {"idInputName": "ComponentId", "type": "ApplicationInsights"}}}, {"position": {"x": 5, "y": 0, "colSpan": 1, "rowSpan": 1}, "metadata": {"inputs": [{"name": "ComponentId", "value": {"Name": "[parameters('applicationInsightsName')]", "SubscriptionId": "[subscription().subscriptionId]", "ResourceGroup": "[resourceGroup().name]"}}, {"name": "TimeContext", "value": {"durationMs": 86400000, "endTime": null, "createdTime": "2018-05-08T18:47:35.237Z", "isInitialTime": true, "grain": 1, "useDashboardTimeRange": false}}, {"name": "ConfigurationId", "value": "78ce933e-e864-4b05-a27b-71fd55a6afad"}], "type": "Extension/AppInsightsExtension/PartType/AppMapButtonPart", "asset": {"idInputName": "ComponentId", "type": "ApplicationInsights"}}}, {"position": {"x": 0, "y": 1, "colSpan": 3, "rowSpan": 1}, "metadata": {"inputs": [], "type": "Extension/HubsExtension/PartType/MarkdownPart", "settings": {"content": {"settings": {"content": "# Usage", "title": "", "subtitle": ""}}}}}, {"position": {"x": 3, "y": 1, "colSpan": 1, "rowSpan": 1}, "metadata": {"inputs": [{"name": "ComponentId", "value": {"Name": "[parameters('applicationInsightsName')]", "SubscriptionId": "[subscription().subscriptionId]", "ResourceGroup": "[resourceGroup().name]"}}, {"name": "TimeContext", "value": {"durationMs": 86400000, "endTime": null, "createdTime": "2018-05-04T01:22:35.782Z", "isInitialTime": true, "grain": 1, "useDashboardTimeRange": false}}], "type": "Extension/AppInsightsExtension/PartType/UsageUsersOverviewPart", "asset": {"idInputName": "ComponentId", "type": "ApplicationInsights"}}}, {"position": {"x": 4, "y": 1, "colSpan": 3, "rowSpan": 1}, "metadata": {"inputs": [], "type": "Extension/HubsExtension/PartType/MarkdownPart", "settings": {"content": {"settings": {"content": "# Reliability", "title": "", "subtitle": ""}}}}}, {"position": {"x": 7, "y": 1, "colSpan": 1, "rowSpan": 1}, "metadata": {"inputs": [{"name": "ResourceId", "value": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, {"name": "DataModel", "value": {"version": "1.0.0", "timeContext": {"durationMs": 86400000, "createdTime": "2018-05-04T23:42:40.072Z", "isInitialTime": false, "grain": 1, "useDashboardTimeRange": false}}, "isOptional": true}, {"name": "ConfigurationId", "value": "8a02f7bf-ac0f-40e1-afe9-f0e72cfee77f", "isOptional": true}], "type": "Extension/AppInsightsExtension/PartType/CuratedBladeFailuresPinnedPart", "isAdapter": true, "asset": {"idInputName": "ResourceId", "type": "ApplicationInsights"}, "defaultMenuItemId": "failures"}}, {"position": {"x": 8, "y": 1, "colSpan": 3, "rowSpan": 1}, "metadata": {"inputs": [], "type": "Extension/HubsExtension/PartType/MarkdownPart", "settings": {"content": {"settings": {"content": "# Responsiveness\r\n", "title": "", "subtitle": ""}}}}}, {"position": {"x": 11, "y": 1, "colSpan": 1, "rowSpan": 1}, "metadata": {"inputs": [{"name": "ResourceId", "value": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, {"name": "DataModel", "value": {"version": "1.0.0", "timeContext": {"durationMs": 86400000, "createdTime": "2018-05-04T23:43:37.804Z", "isInitialTime": false, "grain": 1, "useDashboardTimeRange": false}}, "isOptional": true}, {"name": "ConfigurationId", "value": "2a8ede4f-2bee-4b9c-aed9-2db0e8a01865", "isOptional": true}], "type": "Extension/AppInsightsExtension/PartType/CuratedBladePerformancePinnedPart", "isAdapter": true, "asset": {"idInputName": "ResourceId", "type": "ApplicationInsights"}, "defaultMenuItemId": "performance"}}, {"position": {"x": 12, "y": 1, "colSpan": 3, "rowSpan": 1}, "metadata": {"inputs": [], "type": "Extension/HubsExtension/PartType/MarkdownPart", "settings": {"content": {"settings": {"content": "# Browser", "title": "", "subtitle": ""}}}}}, {"position": {"x": 15, "y": 1, "colSpan": 1, "rowSpan": 1}, "metadata": {"inputs": [{"name": "ComponentId", "value": {"Name": "[parameters('applicationInsightsName')]", "SubscriptionId": "[subscription().subscriptionId]", "ResourceGroup": "[resourceGroup().name]"}}, {"name": "MetricsExplorerJsonDefinitionId", "value": "BrowserPerformanceTimelineMetrics"}, {"name": "TimeContext", "value": {"durationMs": 86400000, "createdTime": "2018-05-08T12:16:27.534Z", "isInitialTime": false, "grain": 1, "useDashboardTimeRange": false}}, {"name": "<PERSON><PERSON><PERSON>er", "value": {"eventTypes": [4, 1, 3, 5, 2, 6, 13], "typeFacets": {}, "isPermissive": false}}, {"name": "id", "value": {"Name": "[parameters('applicationInsightsName')]", "SubscriptionId": "[subscription().subscriptionId]", "ResourceGroup": "[resourceGroup().name]"}}, {"name": "Version", "value": "1.0"}], "type": "Extension/AppInsightsExtension/PartType/MetricsExplorerBladePinnedPart", "asset": {"idInputName": "ComponentId", "type": "ApplicationInsights"}, "defaultMenuItemId": "browser"}}, {"position": {"x": 0, "y": 2, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "sessions/count", "aggregationType": 5, "namespace": "microsoft.insights/components/kusto", "metricVisualization": {"displayName": "Sessions", "color": "#47BDF5"}}, {"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "users/count", "aggregationType": 5, "namespace": "microsoft.insights/components/kusto", "metricVisualization": {"displayName": "Users", "color": "#7E58FF"}}], "title": "Unique sessions and users", "visualization": {"chartType": 2, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}, "openBladeOnClick": {"openBlade": true, "destinationBlade": {"extensionName": "HubsExtension", "bladeName": "ResourceMenuBlade", "parameters": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]", "menuid": "segmentationUsers"}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}, {"position": {"x": 4, "y": 2, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "requests/failed", "aggregationType": 7, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Failed requests", "color": "#EC008C"}}], "title": "Failed requests", "visualization": {"chartType": 3, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}, "openBladeOnClick": {"openBlade": true, "destinationBlade": {"extensionName": "HubsExtension", "bladeName": "ResourceMenuBlade", "parameters": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]", "menuid": "failures"}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}, {"position": {"x": 8, "y": 2, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "requests/duration", "aggregationType": 4, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Server response time", "color": "#00BCF2"}}], "title": "Server response time", "visualization": {"chartType": 2, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}, "openBladeOnClick": {"openBlade": true, "destinationBlade": {"extensionName": "HubsExtension", "bladeName": "ResourceMenuBlade", "parameters": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]", "menuid": "performance"}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}, {"position": {"x": 12, "y": 2, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "browserTimings/networkDuration", "aggregationType": 4, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Page load network connect time", "color": "#7E58FF"}}, {"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "browserTimings/processingDuration", "aggregationType": 4, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Client processing time", "color": "#44F1C8"}}, {"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "browserTimings/sendDuration", "aggregationType": 4, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Send request time", "color": "#EB9371"}}, {"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "browserTimings/receiveDuration", "aggregationType": 4, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Receiving response time", "color": "#0672F1"}}], "title": "Average page load time breakdown", "visualization": {"chartType": 3, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}, {"position": {"x": 0, "y": 5, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "availabilityResults/availabilityPercentage", "aggregationType": 4, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Availability", "color": "#47BDF5"}}], "title": "Average availability", "visualization": {"chartType": 3, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}, "openBladeOnClick": {"openBlade": true, "destinationBlade": {"extensionName": "HubsExtension", "bladeName": "ResourceMenuBlade", "parameters": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]", "menuid": "availability"}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}, {"position": {"x": 4, "y": 5, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "exceptions/server", "aggregationType": 7, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Server exceptions", "color": "#47BDF5"}}, {"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "dependencies/failed", "aggregationType": 7, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Dependency failures", "color": "#7E58FF"}}], "title": "Server exceptions and Dependency failures", "visualization": {"chartType": 2, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}, {"position": {"x": 8, "y": 5, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "performanceCounters/processorCpuPercentage", "aggregationType": 4, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Processor time", "color": "#47BDF5"}}, {"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "performanceCounters/processCpuPercentage", "aggregationType": 4, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Process CPU", "color": "#7E58FF"}}], "title": "Average processor and process CPU utilization", "visualization": {"chartType": 2, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}, {"position": {"x": 12, "y": 5, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "exceptions/browser", "aggregationType": 7, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Browser exceptions", "color": "#47BDF5"}}], "title": "Browser exceptions", "visualization": {"chartType": 2, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}, {"position": {"x": 0, "y": 8, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "availabilityResults/count", "aggregationType": 7, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Availability test results count", "color": "#47BDF5"}}], "title": "Availability test results count", "visualization": {"chartType": 2, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}, {"position": {"x": 4, "y": 8, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "performanceCounters/processIOBytesPerSecond", "aggregationType": 4, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Process IO rate", "color": "#47BDF5"}}], "title": "Average process I/O rate", "visualization": {"chartType": 2, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}, {"position": {"x": 8, "y": 8, "colSpan": 4, "rowSpan": 3}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "[format('/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Insights/components/{2}', subscription().subscriptionId, resourceGroup().name, parameters('applicationInsightsName'))]"}, "name": "performanceCounters/memoryAvailableBytes", "aggregationType": 4, "namespace": "microsoft.insights/components", "metricVisualization": {"displayName": "Available memory", "color": "#47BDF5"}}], "title": "Average available memory", "visualization": {"chartType": 2, "legendVisualization": {"isVisible": true, "position": 2, "hideSubtitle": false}, "axisVisualization": {"x": {"isVisible": true, "axisType": 2}, "y": {"isVisible": true, "axisType": 1}}}}}}, {"name": "sharedTimeRange", "isOptional": true}], "type": "Extension/HubsExtension/PartType/MonitorChartPart", "settings": {}}}]}]}}]}}, "dependsOn": ["[resourceId('Microsoft.Insights/components', parameters('name'))]"]}], "outputs": {"connectionString": {"type": "string", "value": "[reference(resourceId('Microsoft.Insights/components', parameters('name')), '2020-02-02').ConnectionString]"}, "id": {"type": "string", "value": "[resourceId('Microsoft.Insights/components', parameters('name'))]"}, "instrumentationKey": {"type": "string", "value": "[reference(resourceId('Microsoft.Insights/components', parameters('name')), '2020-02-02').InstrumentationKey]"}, "name": {"type": "string", "value": "[parameters('name')]"}}}}, "dependsOn": ["[resourceId('Microsoft.Resources/deployments', 'logAnalytics')]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "cognitiveServices", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "name": {"value": "[parameters('aiServicesName')]"}, "kind": {"value": "AIServices"}, "deployments": {"value": "[parameters('aiServiceModelDeployments')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "13237299269915090657"}, "description": "Creates an Azure Cognitive Services instance."}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "customSubDomainName": {"type": "string", "defaultValue": "[parameters('name')]", "metadata": {"description": "The custom subdomain name used to access the API. Defaults to the value of the name parameter."}}, "disableLocalAuth": {"type": "bool", "defaultValue": false}, "deployments": {"type": "array", "defaultValue": []}, "kind": {"type": "string", "defaultValue": "OpenAI"}, "publicNetworkAccess": {"type": "string", "defaultValue": "Enabled", "allowedValues": ["Enabled", "Disabled"]}, "sku": {"type": "object", "defaultValue": {"name": "S0"}}, "allowedIpRules": {"type": "array", "defaultValue": []}, "networkAcls": {"type": "object", "defaultValue": "[if(empty(parameters('allowedIpRules')), createObject('defaultAction', 'Allow'), createObject('ipRules', parameters('allowedIpRules'), 'defaultAction', 'Deny'))]"}}, "resources": [{"type": "Microsoft.CognitiveServices/accounts", "apiVersion": "2023-05-01", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "kind": "[parameters('kind')]", "properties": {"customSubDomainName": "[parameters('customSubDomainName')]", "publicNetworkAccess": "[parameters('publicNetworkAccess')]", "networkAcls": "[parameters('networkAcls')]", "disableLocalAuth": "[parameters('disableLocalAuth')]"}, "sku": "[parameters('sku')]"}, {"copy": {"name": "deployment", "count": "[length(parameters('deployments'))]", "mode": "serial", "batchSize": 1}, "type": "Microsoft.CognitiveServices/accounts/deployments", "apiVersion": "2023-05-01", "name": "[format('{0}/{1}', parameters('name'), parameters('deployments')[copyIndex()].name)]", "properties": {"model": "[parameters('deployments')[copyIndex()].model]", "raiPolicyName": "[if(contains(parameters('deployments')[copyIndex()], 'raiPolicyName'), parameters('deployments')[copyIndex()].raiPolicyName, null())]"}, "sku": "[if(contains(parameters('deployments')[copyIndex()], 'sku'), parameters('deployments')[copyIndex()].sku, createObject('name', 'Standard', 'capacity', 20))]", "dependsOn": ["[resourceId('Microsoft.CognitiveServices/accounts', parameters('name'))]"]}], "outputs": {"endpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.CognitiveServices/accounts', parameters('name')), '2023-05-01').endpoint]"}, "endpoints": {"type": "object", "value": "[reference(resourceId('Microsoft.CognitiveServices/accounts', parameters('name')), '2023-05-01').endpoints]"}, "id": {"type": "string", "value": "[resourceId('Microsoft.CognitiveServices/accounts', parameters('name'))]"}, "name": {"type": "string", "value": "[parameters('name')]"}}}}}, {"condition": "[not(empty(parameters('searchServiceName')))]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "searchService", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "name": {"value": "[parameters('searchServiceName')]"}, "semanticSearch": {"value": "free"}, "authOptions": {"value": {"aadOrApiKey": {"aadAuthFailureMode": "http401WithBearerChallenge"}}}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "15702788402850698618"}, "description": "Creates an Azure AI Search instance."}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "sku": {"type": "object", "defaultValue": {"name": "standard"}}, "authOptions": {"type": "object", "defaultValue": {}}, "disableLocalAuth": {"type": "bool", "defaultValue": false}, "disabledDataExfiltrationOptions": {"type": "array", "defaultValue": []}, "encryptionWithCmk": {"type": "object", "defaultValue": {"enforcement": "Unspecified"}}, "hostingMode": {"type": "string", "defaultValue": "default", "allowedValues": ["default", "highDensity"]}, "networkRuleSet": {"type": "object", "defaultValue": {"bypass": "None", "ipRules": []}}, "partitionCount": {"type": "int", "defaultValue": 1}, "publicNetworkAccess": {"type": "string", "defaultValue": "enabled", "allowedValues": ["enabled", "disabled"]}, "replicaCount": {"type": "int", "defaultValue": 1}, "semanticSearch": {"type": "string", "defaultValue": "disabled", "allowedValues": ["disabled", "free", "standard"]}}, "variables": {"searchIdentityProvider": "[if(equals(parameters('sku').name, 'free'), null(), createObject('type', 'SystemAssigned'))]"}, "resources": [{"type": "Microsoft.Search/searchServices", "apiVersion": "2021-04-01-preview", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "identity": "[variables('searchIdentityProvider')]", "properties": {"authOptions": "[parameters('authOptions')]", "disableLocalAuth": "[parameters('disableLocalAuth')]", "disabledDataExfiltrationOptions": "[parameters('disabledDataExfiltrationOptions')]", "encryptionWithCmk": "[parameters('encryptionWithCmk')]", "hostingMode": "[parameters('hostingMode')]", "networkRuleSet": "[parameters('networkRuleSet')]", "partitionCount": "[parameters('partitionCount')]", "publicNetworkAccess": "[parameters('publicNetworkAccess')]", "replicaCount": "[parameters('replicaCount')]", "semanticSearch": "[parameters('semanticSearch')]"}, "sku": "[parameters('sku')]"}], "outputs": {"id": {"type": "string", "value": "[resourceId('Microsoft.Search/searchServices', parameters('name'))]"}, "endpoint": {"type": "string", "value": "[format('https://{0}.search.windows.net/', parameters('name'))]"}, "name": {"type": "string", "value": "[parameters('name')]"}, "principalId": {"type": "string", "value": "[if(not(empty(variables('searchIdentityProvider'))), reference(resourceId('Microsoft.Search/searchServices', parameters('name')), '2021-04-01-preview', 'full').identity.principalId, '')]"}}}}}], "outputs": {"keyVaultId": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'keyvault'), '2022-09-01').outputs.id.value]"}, "keyVaultName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'keyvault'), '2022-09-01').outputs.name.value]"}, "keyVaultEndpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'keyvault'), '2022-09-01').outputs.endpoint.value]"}, "storageAccountId": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'storageAccount'), '2022-09-01').outputs.id.value]"}, "storageAccountName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'storageAccount'), '2022-09-01').outputs.name.value]"}, "applicationInsightsId": {"type": "string", "value": "[if(not(empty(parameters('applicationInsightsName'))), reference(resourceId('Microsoft.Resources/deployments', 'applicationInsights'), '2022-09-01').outputs.id.value, '')]"}, "applicationInsightsName": {"type": "string", "value": "[if(not(empty(parameters('applicationInsightsName'))), reference(resourceId('Microsoft.Resources/deployments', 'applicationInsights'), '2022-09-01').outputs.name.value, '')]"}, "logAnalyticsWorkspaceId": {"type": "string", "value": "[if(not(empty(parameters('logAnalyticsName'))), reference(resourceId('Microsoft.Resources/deployments', 'logAnalytics'), '2022-09-01').outputs.id.value, '')]"}, "logAnalyticsWorkspaceName": {"type": "string", "value": "[if(not(empty(parameters('logAnalyticsName'))), reference(resourceId('Microsoft.Resources/deployments', 'logAnalytics'), '2022-09-01').outputs.name.value, '')]"}, "aiServiceId": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'cognitiveServices'), '2022-09-01').outputs.id.value]"}, "aiServicesName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'cognitiveServices'), '2022-09-01').outputs.name.value]"}, "aiServiceEndpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'cognitiveServices'), '2022-09-01').outputs.endpoints.value['OpenAI Language Model Instance API']]"}, "searchServiceId": {"type": "string", "value": "[if(not(empty(parameters('searchServiceName'))), reference(resourceId('Microsoft.Resources/deployments', 'searchService'), '2022-09-01').outputs.id.value, '')]"}, "searchServiceName": {"type": "string", "value": "[if(not(empty(parameters('searchServiceName'))), reference(resourceId('Microsoft.Resources/deployments', 'searchService'), '2022-09-01').outputs.name.value, '')]"}, "searchServiceEndpoint": {"type": "string", "value": "[if(not(empty(parameters('searchServiceName'))), reference(resourceId('Microsoft.Resources/deployments', 'searchService'), '2022-09-01').outputs.endpoint.value, '')]"}}}}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "hub", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "name": {"value": "[parameters('hubName')]"}, "displayName": {"value": "[parameters('hubName')]"}, "keyVaultId": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.keyVaultId.value]"}, "storageAccountId": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.storageAccountId.value]"}, "applicationInsightsId": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.applicationInsightsId.value]"}, "aiServicesName": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.aiServicesName.value]"}, "aiServicesConnectionName": {"value": "[parameters('aiServicesConnectionName')]"}, "aiSearchName": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.searchServiceName.value]"}, "aiSearchConnectionName": {"value": "[parameters('searchConnectionName')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "13413949643186628139"}}, "parameters": {"name": {"type": "string", "metadata": {"description": "The AI Foundry Hub Resource name"}}, "displayName": {"type": "string", "defaultValue": "[parameters('name')]", "metadata": {"description": "The display name of the AI Foundry Hub Resource"}}, "storageAccountId": {"type": "string", "metadata": {"description": "The storage account ID to use for the AI Foundry Hub Resource"}}, "keyVaultId": {"type": "string", "metadata": {"description": "The key vault ID to use for the AI Foundry Hub Resource"}}, "applicationInsightsId": {"type": "string", "defaultValue": "", "metadata": {"description": "The application insights ID to use for the AI Foundry Hub Resource"}}, "aiServicesName": {"type": "string", "metadata": {"description": "The AI Services account name to use for the AI Foundry Hub Resource"}}, "aiServicesConnectionName": {"type": "string", "metadata": {"description": "The AI Services connection name to use for the AI Foundry Hub Resource"}}, "aiSearchName": {"type": "string", "defaultValue": "", "metadata": {"description": "The Azure Cognitive Search service name to use for the AI Foundry Hub Resource"}}, "aiSearchConnectionName": {"type": "string", "metadata": {"description": "The Azure Cognitive Search service connection name to use for the AI Foundry Hub Resource"}}, "skuName": {"type": "string", "defaultValue": "Basic", "metadata": {"description": "The SKU name to use for the AI Foundry Hub Resource"}}, "skuTier": {"type": "string", "defaultValue": "Basic", "allowedValues": ["Basic", "Free", "Premium", "Standard"], "metadata": {"description": "The SKU tier to use for the AI Foundry Hub Resource"}}, "publicNetworkAccess": {"type": "string", "defaultValue": "Enabled", "allowedValues": ["Enabled", "Disabled"], "metadata": {"description": "The public network access setting to use for the AI Foundry Hub Resource"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}}, "resources": [{"type": "Microsoft.MachineLearningServices/workspaces/connections", "apiVersion": "2024-07-01-preview", "name": "[format('{0}/{1}', parameters('name'), parameters('aiServicesConnectionName'))]", "properties": {"category": "AIServices", "authType": "<PERSON><PERSON><PERSON><PERSON>", "isSharedToAll": true, "target": "[reference(resourceId('Microsoft.CognitiveServices/accounts', parameters('aiServicesName')), '2023-05-01').endpoint]", "metadata": {"ApiVersion": "2023-07-01-preview", "ApiType": "azure", "ResourceId": "[resourceId('Microsoft.CognitiveServices/accounts', parameters('aiServicesName'))]"}, "credentials": {"key": "[listKeys(resourceId('Microsoft.CognitiveServices/accounts', parameters('aiServicesName')), '2023-05-01').key1]"}}, "dependsOn": ["[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name'))]"]}, {"condition": "[not(empty(parameters('aiSearchName')))]", "type": "Microsoft.MachineLearningServices/workspaces/connections", "apiVersion": "2024-07-01-preview", "name": "[format('{0}/{1}', parameters('name'), parameters('aiSearchConnectionName'))]", "properties": {"category": "CognitiveSearch", "authType": "<PERSON><PERSON><PERSON><PERSON>", "isSharedToAll": true, "target": "[format('https://{0}.search.windows.net/', parameters('aiSearchName'))]", "credentials": {"key": "[if(not(empty(parameters('aiSearchName'))), listAdminKeys(resourceId('Microsoft.Search/searchServices', parameters('aiSearchName')), '2021-04-01-preview').primaryKey, '')]"}}, "dependsOn": ["[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name'))]"]}, {"type": "Microsoft.MachineLearningServices/workspaces", "apiVersion": "2024-07-01-preview", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "sku": {"name": "[parameters('skuName')]", "tier": "[parameters('skuTier')]"}, "kind": "<PERSON><PERSON>", "identity": {"type": "SystemAssigned"}, "properties": {"friendlyName": "[parameters('displayName')]", "storageAccount": "[parameters('storageAccountId')]", "keyVault": "[parameters('keyVaultId')]", "applicationInsights": "[if(not(empty(parameters('applicationInsightsId'))), parameters('applicationInsightsId'), null())]", "hbiWorkspace": false, "managedNetwork": {"isolationMode": "Disabled"}, "v1LegacyMode": false, "publicNetworkAccess": "[parameters('publicNetworkAccess')]"}}], "outputs": {"name": {"type": "string", "value": "[parameters('name')]"}, "id": {"type": "string", "value": "[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name'))]"}, "principalId": {"type": "string", "value": "[reference(resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name')), '2024-07-01-preview', 'full').identity.principalId]"}}}}, "dependsOn": ["[resourceId('Microsoft.Resources/deployments', 'hubDependencies')]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "project", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "name": {"value": "[parameters('projectName')]"}, "displayName": {"value": "[parameters('projectName')]"}, "hubName": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'hub'), '2022-09-01').outputs.name.value]"}, "keyVaultName": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.keyVaultName.value]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "1627900001840505883"}}, "parameters": {"name": {"type": "string", "metadata": {"description": "The AI Foundry Hub Resource name"}}, "displayName": {"type": "string", "defaultValue": "[parameters('name')]", "metadata": {"description": "The display name of the AI Foundry Hub Resource"}}, "hubName": {"type": "string", "metadata": {"description": "The name of the AI Foundry Hub Resource where this project should be created"}}, "keyVaultName": {"type": "string", "metadata": {"description": "The name of the key vault resource to grant access to the project"}}, "skuName": {"type": "string", "defaultValue": "Basic", "metadata": {"description": "The SKU name to use for the AI Foundry Hub Resource"}}, "skuTier": {"type": "string", "defaultValue": "Basic", "allowedValues": ["Basic", "Free", "Premium", "Standard"], "metadata": {"description": "The SKU tier to use for the AI Foundry Hub Resource"}}, "publicNetworkAccess": {"type": "string", "defaultValue": "Enabled", "allowedValues": ["Enabled", "Disabled"], "metadata": {"description": "The public network access setting to use for the AI Foundry Hub Resource"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}}, "resources": [{"type": "Microsoft.MachineLearningServices/workspaces", "apiVersion": "2024-01-01-preview", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "sku": {"name": "[parameters('skuName')]", "tier": "[parameters('skuTier')]"}, "kind": "Project", "identity": {"type": "SystemAssigned"}, "properties": {"friendlyName": "[parameters('displayName')]", "hbiWorkspace": false, "v1LegacyMode": false, "publicNetworkAccess": "[parameters('publicNetworkAccess')]", "hubResourceId": "[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('hubName'))]"}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "keyvault-access", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"keyVaultName": {"value": "[parameters('keyVaultName')]"}, "principalId": {"value": "[reference(resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name')), '2024-01-01-preview', 'full').identity.principalId]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "3777159230715895126"}, "description": "Assigns an Azure Key Vault access policy."}, "parameters": {"name": {"type": "string", "defaultValue": "add"}, "keyVaultName": {"type": "string"}, "permissions": {"type": "object", "defaultValue": {"secrets": ["get", "list"]}}, "principalId": {"type": "string"}}, "resources": [{"type": "Microsoft.KeyVault/vaults/accessPolicies", "apiVersion": "2022-07-01", "name": "[format('{0}/{1}', parameters('keyVaultName'), parameters('name'))]", "properties": {"accessPolicies": [{"objectId": "[parameters('principalId')]", "tenantId": "[subscription().tenantId]", "permissions": "[parameters('permissions')]"}]}}]}}, "dependsOn": ["[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name'))]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "ml-service-role-data-scientist", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name')), '2024-01-01-preview', 'full').identity.principalId]"}, "roleDefinitionId": {"value": "f6c7c914-8db3-469d-8ca1-694a8f32e121"}, "principalType": {"value": "ServicePrincipal"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name'))]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "ml-service-role-secrets-reader", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name')), '2024-01-01-preview', 'full').identity.principalId]"}, "roleDefinitionId": {"value": "ea01e6af-a1c1-4350-9563-ad00f8c72ec5"}, "principalType": {"value": "ServicePrincipal"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name'))]"]}], "outputs": {"id": {"type": "string", "value": "[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name'))]"}, "name": {"type": "string", "value": "[parameters('name')]"}, "principalId": {"type": "string", "value": "[reference(resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name')), '2024-01-01-preview', 'full').identity.principalId]"}, "discoveryUrl": {"type": "string", "value": "[reference(resourceId('Microsoft.MachineLearningServices/workspaces', parameters('name')), '2024-01-01-preview').discoveryUrl]"}}}}, "dependsOn": ["[resourceId('Microsoft.Resources/deployments', 'hub')]", "[resourceId('Microsoft.Resources/deployments', 'hubDependencies')]"]}], "outputs": {"resourceGroupName": {"type": "string", "value": "[resourceGroup().name]"}, "hubName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hub'), '2022-09-01').outputs.name.value]"}, "hubPrincipalId": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hub'), '2022-09-01').outputs.principalId.value]"}, "projectName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'project'), '2022-09-01').outputs.name.value]"}, "projectPrincipalId": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'project'), '2022-09-01').outputs.principalId.value]"}, "keyVaultName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.keyVaultName.value]"}, "keyVaultEndpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.keyVaultEndpoint.value]"}, "applicationInsightsName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.applicationInsightsName.value]"}, "logAnalyticsWorkspaceName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.logAnalyticsWorkspaceName.value]"}, "storageAccountName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.storageAccountName.value]"}, "aiServicesName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.aiServicesName.value]"}, "aiServiceEndpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.aiServiceEndpoint.value]"}, "searchServiceName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.searchServiceName.value]"}, "searchServiceEndpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'hubDependencies'), '2022-09-01').outputs.searchServiceEndpoint.value]"}, "discoveryUrl": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'project'), '2022-09-01').outputs.discoveryUrl.value]"}}}}, "dependsOn": ["[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"condition": "[not(empty(parameters('aiExistingProjectConnectionString')))]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "logAnalytics", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"location": {"value": "[parameters('location')]"}, "tags": {"value": "[variables('tags')]"}, "name": {"value": "[variables('logAnalyticsWorkspaceResolvedName')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "*****************"}, "description": "Creates a Log Analytics workspace."}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}}, "resources": [{"type": "Microsoft.OperationalInsights/workspaces", "apiVersion": "2021-12-01-preview", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "properties": {"retentionInDays": 30, "features": {"searchVersion": 1}, "sku": {"name": "PerGB2018"}}}], "outputs": {"id": {"type": "string", "value": "[resourceId('Microsoft.OperationalInsights/workspaces', parameters('name'))]"}, "name": {"type": "string", "value": "[parameters('name')]"}}}}, "dependsOn": ["[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"condition": "[not(empty(variables('resolvedApplicationInsightsName')))]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "monitoringmetricscontributor-role-azureai-developer-rg", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"appInsightsName": {"value": "[variables('resolvedApplicationInsightsName')]"}, "principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "14860267170148532952"}, "description": "Assigns MonitoringMetricsContributor role to Application Insights."}, "parameters": {"appInsightsName": {"type": "string"}, "principalId": {"type": "string"}}, "variables": {"monitoringMetricsContributorRole": "[subscriptionResourceId('Microsoft.Authorization/roleDefinitions', '749f88d5-cbae-40b8-bcfc-e573ddc772fa')]"}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "scope": "[format('Microsoft.Insights/components/{0}', parameters('appInsightsName'))]", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), variables('monitoringMetricsContributorRole'))]", "properties": {"roleDefinitionId": "[variables('monitoringMetricsContributorRole')]", "principalId": "[parameters('principalId')]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"condition": "[not(empty(parameters('aiExistingProjectConnectionString')))]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "backend-role-azureai-developer-existing-project-rg", "resourceGroup": "[split(parameters('aiExistingProjectConnectionString'), ';')[2]]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "64702f94-c441-49e6-a78b-ef80e0188fee"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "container-apps", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"name": {"value": "app"}, "location": {"value": "[parameters('location')]"}, "tags": {"value": "[variables('tags')]"}, "containerAppsEnvironmentName": {"value": "[format('containerapps-env-{0}', variables('resourceToken'))]"}, "logAnalyticsWorkspaceName": "[if(empty(parameters('aiExistingProjectConnectionString')), createObject('value', reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.logAnalyticsWorkspaceName.value), createObject('value', reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'logAnalytics'), '2022-09-01').outputs.name.value))]"}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "11655435555249683948"}, "description": "Creates an Azure Container Apps environment."}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "containerAppsEnvironmentName": {"type": "string"}, "logAnalyticsWorkspaceName": {"type": "string"}, "applicationInsightsName": {"type": "string", "defaultValue": ""}}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "[format('{0}-container-apps-environment', parameters('name'))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"name": {"value": "[parameters('containerAppsEnvironmentName')]"}, "location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "logAnalyticsWorkspaceName": {"value": "[parameters('logAnalyticsWorkspaceName')]"}, "applicationInsightsName": {"value": "[parameters('applicationInsightsName')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "11331806587581464666"}, "description": "Creates an Azure Container Apps environment."}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "applicationInsightsName": {"type": "string", "defaultValue": "", "metadata": {"description": "Name of the Application Insights resource"}}, "daprEnabled": {"type": "bool", "defaultValue": false, "metadata": {"description": "Specifies if <PERSON><PERSON><PERSON> is enabled"}}, "logAnalyticsWorkspaceName": {"type": "string", "metadata": {"description": "Name of the Log Analytics workspace"}}}, "resources": [{"type": "Microsoft.App/managedEnvironments", "apiVersion": "2023-05-01", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "properties": {"appLogsConfiguration": {"destination": "log-analytics", "logAnalyticsConfiguration": {"customerId": "[reference(resourceId('Microsoft.OperationalInsights/workspaces', parameters('logAnalyticsWorkspaceName')), '2022-10-01').customerId]", "sharedKey": "[listKeys(resourceId('Microsoft.OperationalInsights/workspaces', parameters('logAnalyticsWorkspaceName')), '2022-10-01').primarySharedKey]"}}, "daprAIInstrumentationKey": "[if(and(parameters('daprEnabled'), not(empty(parameters('applicationInsightsName')))), reference(resourceId('Microsoft.Insights/components', parameters('applicationInsightsName')), '2020-02-02').InstrumentationKey, '')]"}}], "outputs": {"defaultDomain": {"type": "string", "value": "[reference(resourceId('Microsoft.App/managedEnvironments', parameters('name')), '2023-05-01').defaultDomain]"}, "id": {"type": "string", "value": "[resourceId('Microsoft.App/managedEnvironments', parameters('name'))]"}, "name": {"type": "string", "value": "[parameters('name')]"}}}}}], "outputs": {"defaultDomain": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', format('{0}-container-apps-environment', parameters('name'))), '2022-09-01').outputs.defaultDomain.value]"}, "environmentName": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', format('{0}-container-apps-environment', parameters('name'))), '2022-09-01').outputs.name.value]"}, "environmentId": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', format('{0}-container-apps-environment', parameters('name'))), '2022-09-01').outputs.id.value]"}}}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai')]", "[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'logAnalytics')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "api", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"name": {"value": "[format('ca-api-{0}', variables('resourceToken'))]"}, "location": {"value": "[parameters('location')]"}, "tags": {"value": "[variables('tags')]"}, "identityName": {"value": "[format('{0}api-{1}', variables('abbrs').managedIdentityUserAssignedIdentities, variables('resourceToken'))]"}, "containerAppsEnvironmentName": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'container-apps'), '2022-09-01').outputs.environmentName.value]"}, "projectConnectionString": "[if(empty(if(and(and(empty(parameters('aiExistingProjectConnectionString')), not(empty(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value))), contains(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value, '/')), split(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value, '/')[2], '')), createObject('value', parameters('aiExistingProjectConnectionString')), createObject('value', format('{0};{1};{2};{3}', if(and(and(empty(parameters('aiExistingProjectConnectionString')), not(empty(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value))), contains(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value, '/')), split(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value, '/')[2], ''), subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))), variables('projectName'))))]", "agentDeploymentName": {"value": "[parameters('agentDeploymentName')]"}, "searchConnectionName": {"value": "[parameters('searchConnectionName')]"}, "aiSearchIndexName": {"value": "[parameters('aiSearchIndexName')]"}, "searchServiceEndpoint": "[if(not(parameters('useSearchService')), createObject('value', ''), createObject('value', reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.searchServiceEndpoint.value))]", "embeddingDeploymentName": {"value": "[parameters('embeddingDeploymentName')]"}, "embeddingDeploymentDimensions": {"value": "[parameters('embeddingDeploymentDimensions')]"}, "agentName": {"value": "[parameters('agentName')]"}, "agentID": {"value": "[variables('agentID')]"}, "projectName": {"value": "[variables('projectName')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "8075064967424346344"}}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "identityName": {"type": "string"}, "containerAppsEnvironmentName": {"type": "string"}, "projectConnectionString": {"type": "string"}, "agentDeploymentName": {"type": "string"}, "searchConnectionName": {"type": "string"}, "embeddingDeploymentName": {"type": "string"}, "aiSearchIndexName": {"type": "string"}, "embeddingDeploymentDimensions": {"type": "string"}, "searchServiceEndpoint": {"type": "string"}, "agentName": {"type": "string"}, "agentID": {"type": "string"}, "projectName": {"type": "string"}}, "resources": [{"type": "Microsoft.ManagedIdentity/userAssignedIdentities", "apiVersion": "2023-01-31", "name": "[parameters('identityName')]", "location": "[parameters('location')]"}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "container-app-module", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"name": {"value": "[parameters('name')]"}, "location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "identityName": {"value": "[parameters('identityName')]"}, "containerAppsEnvironmentName": {"value": "[parameters('containerAppsEnvironmentName')]"}, "targetPort": {"value": 50505}, "env": {"value": [{"name": "AZURE_CLIENT_ID", "value": "[reference(resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', parameters('identityName')), '2023-01-31').clientId]"}, {"name": "AZURE_EXISTING_AIPROJECT_CONNECTION_STRING", "value": "[parameters('projectConnectionString')]"}, {"name": "AZURE_AI_AGENT_NAME", "value": "[parameters('agentName')]"}, {"name": "AZURE_EXISTING_AGENT_ID", "value": "[parameters('agentID')]"}, {"name": "AZURE_AI_AGENT_DEPLOYMENT_NAME", "value": "[parameters('agentDeploymentName')]"}, {"name": "AZURE_AI_EMBED_DEPLOYMENT_NAME", "value": "[parameters('embeddingDeploymentName')]"}, {"name": "AZURE_AI_SEARCH_INDEX_NAME", "value": "[parameters('aiSearchIndexName')]"}, {"name": "AZURE_AI_EMBED_DIMENSIONS", "value": "[parameters('embeddingDeploymentDimensions')]"}, {"name": "RUNNING_IN_PRODUCTION", "value": "true"}, {"name": "AZURE_AI_SEARCH_CONNECTION_NAME", "value": "[parameters('searchConnectionName')]"}, {"name": "AZURE_AI_SEARCH_ENDPOINT", "value": "[parameters('searchServiceEndpoint')]"}]}, "projectName": {"value": "[parameters('projectName')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "12356022388462729996"}, "description": "Creates or updates an existing Azure Container App."}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "containerAppsEnvironmentName": {"type": "string", "metadata": {"description": "The environment name for the container apps"}}, "containerCpuCoreCount": {"type": "string", "defaultValue": "0.5", "metadata": {"description": "The number of CPU cores allocated to a single container instance, e.g., 0.5"}}, "containerMaxReplicas": {"type": "int", "defaultValue": 10, "minValue": 1, "metadata": {"description": "The maximum number of replicas to run. Must be at least 1."}}, "containerMemory": {"type": "string", "defaultValue": "1.0Gi", "metadata": {"description": "The amount of memory allocated to a single container instance, e.g., 1Gi"}}, "containerMinReplicas": {"type": "int", "defaultValue": 1, "minValue": 1, "metadata": {"description": "The minimum number of replicas to run. Must be at least 1."}}, "containerName": {"type": "string", "defaultValue": "main", "metadata": {"description": "The name of the container"}}, "daprAppProtocol": {"type": "string", "defaultValue": "http", "allowedValues": ["http", "grpc"], "metadata": {"description": "The protocol used by Dapr to connect to the app, e.g., HTTP or gRPC"}}, "daprEnabled": {"type": "bool", "defaultValue": false, "metadata": {"description": "Enable or disable Dap<PERSON> for the container app"}}, "daprAppId": {"type": "string", "defaultValue": "[parameters('containerName')]", "metadata": {"description": "The Dapr app ID"}}, "ingressEnabled": {"type": "bool", "defaultValue": true, "metadata": {"description": "Specifies if Ingress is enabled for the container app"}}, "identityType": {"type": "string", "defaultValue": "None", "allowedValues": ["None", "SystemAssigned", "UserAssigned"], "metadata": {"description": "The type of identity for the resource"}}, "identityName": {"type": "string", "defaultValue": "", "metadata": {"description": "The name of the user-assigned identity"}}, "secrets": {"type": "secureObject", "defaultValue": {}, "metadata": {"description": "The secrets required for the container"}}, "env": {"type": "array", "defaultValue": [], "metadata": {"description": "The environment variables for the container"}}, "external": {"type": "bool", "defaultValue": true, "metadata": {"description": "Specifies if the resource ingress is exposed externally"}}, "serviceBinds": {"type": "array", "defaultValue": [], "metadata": {"description": "The service binds associated with the container"}}, "targetPort": {"type": "int", "defaultValue": 80, "metadata": {"description": "The target port for the container"}}, "projectName": {"type": "string"}}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "[format('{0}-update', deployment().name)]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"name": {"value": "[parameters('name')]"}, "location": {"value": "[parameters('location')]"}, "tags": {"value": "[parameters('tags')]"}, "identityType": {"value": "[parameters('identityType')]"}, "identityName": {"value": "[parameters('identityName')]"}, "ingressEnabled": {"value": "[parameters('ingressEnabled')]"}, "containerName": {"value": "[parameters('containerName')]"}, "containerAppsEnvironmentName": {"value": "[parameters('containerAppsEnvironmentName')]"}, "containerCpuCoreCount": {"value": "[parameters('containerCpuCoreCount')]"}, "containerMemory": {"value": "[parameters('containerMemory')]"}, "containerMinReplicas": {"value": "[parameters('containerMinReplicas')]"}, "containerMaxReplicas": {"value": "[parameters('containerMaxReplicas')]"}, "daprEnabled": {"value": "[parameters('daprEnabled')]"}, "daprAppId": {"value": "[parameters('daprAppId')]"}, "daprAppProtocol": {"value": "[parameters('daprAppProtocol')]"}, "secrets": {"value": "[parameters('secrets')]"}, "external": {"value": "[parameters('external')]"}, "env": {"value": "[parameters('env')]"}, "targetPort": {"value": "[parameters('targetPort')]"}, "serviceBinds": {"value": "[parameters('serviceBinds')]"}, "dependOn": {"value": "[parameters('projectName')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "924085996070689995"}, "description": "Creates a container app in an Azure Container App environment."}, "parameters": {"name": {"type": "string"}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]"}, "tags": {"type": "object", "defaultValue": {}}, "allowedOrigins": {"type": "array", "defaultValue": [], "metadata": {"description": "Allowed origins"}}, "containerAppsEnvironmentName": {"type": "string", "metadata": {"description": "Name of the environment for container apps"}}, "containerCpuCoreCount": {"type": "string", "defaultValue": "0.5", "metadata": {"description": "CPU cores allocated to a single container instance, e.g., 0.5"}}, "containerMaxReplicas": {"type": "int", "defaultValue": 10, "minValue": 1, "metadata": {"description": "The maximum number of replicas to run. Must be at least 1."}}, "containerMemory": {"type": "string", "defaultValue": "1.0Gi", "metadata": {"description": "Memory allocated to a single container instance, e.g., 1Gi"}}, "containerMinReplicas": {"type": "int", "defaultValue": 1, "metadata": {"description": "The minimum number of replicas to run. Must be at least 1."}}, "containerName": {"type": "string", "defaultValue": "main", "metadata": {"description": "The name of the container"}}, "daprAppProtocol": {"type": "string", "defaultValue": "http", "allowedValues": ["http", "grpc"], "metadata": {"description": "The protocol used by Dapr to connect to the app, e.g., http or grpc"}}, "daprAppId": {"type": "string", "defaultValue": "[parameters('containerName')]", "metadata": {"description": "The Dapr app ID"}}, "daprEnabled": {"type": "bool", "defaultValue": false, "metadata": {"description": "Enable <PERSON>"}}, "env": {"type": "array", "defaultValue": [], "metadata": {"description": "The environment variables for the container"}}, "external": {"type": "bool", "defaultValue": true, "metadata": {"description": "Specifies if the resource ingress is exposed externally"}}, "identityName": {"type": "string", "defaultValue": "", "metadata": {"description": "The name of the user-assigned identity"}}, "identityType": {"type": "string", "defaultValue": "None", "allowedValues": ["None", "SystemAssigned", "UserAssigned"], "metadata": {"description": "The type of identity for the resource"}}, "ingressEnabled": {"type": "bool", "defaultValue": true, "metadata": {"description": "Specifies if Ingress is enabled for the container app"}}, "revisionMode": {"type": "string", "defaultValue": "Single"}, "dependOn": {"type": "string", "defaultValue": ""}, "secrets": {"type": "secureObject", "defaultValue": {}, "metadata": {"description": "The secrets required for the container"}}, "serviceBinds": {"type": "array", "defaultValue": [], "metadata": {"description": "The service binds associated with the container"}}, "serviceType": {"type": "string", "defaultValue": "", "metadata": {"description": "The name of the container apps add-on to use. e.g. redis"}}, "targetPort": {"type": "int", "defaultValue": 80, "metadata": {"description": "The target port for the container"}}}, "variables": {"normalizedIdentityType": "[if(not(empty(parameters('identityName'))), 'UserAssigned', parameters('identityType'))]"}, "resources": [{"type": "Microsoft.App/containerApps", "apiVersion": "2023-05-02-preview", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "identity": {"type": "[variables('normalizedIdentityType')]", "userAssignedIdentities": "[if(and(not(empty(parameters('identityName'))), equals(variables('normalizedIdentityType'), 'UserAssigned')), createObject(format('{0}', resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', parameters('identityName'))), createObject()), null())]"}, "properties": {"managedEnvironmentId": "[resourceId('Microsoft.App/managedEnvironments', parameters('containerAppsEnvironmentName'))]", "configuration": {"copy": [{"name": "secrets", "count": "[length(items(parameters('secrets')))]", "input": {"name": "[items(parameters('secrets'))[copyIndex('secrets')].key]", "value": "[items(parameters('secrets'))[copyIndex('secrets')].value]"}}], "activeRevisionsMode": "[parameters('revisionMode')]", "ingress": "[if(parameters('ingressEnabled'), createObject('external', parameters('external'), 'targetPort', parameters('targetPort'), 'transport', 'auto', 'corsPolicy', createObject('allowedOrigins', union(createArray('https://portal.azure.com', 'https://ms.portal.azure.com'), parameters('allowedOrigins')))), null())]", "dapr": "[if(parameters('daprEnabled'), createObject('enabled', true(), 'appId', parameters('daprAppId'), 'appProtocol', parameters('daprAppProtocol'), 'appPort', if(parameters('ingressEnabled'), parameters('targetPort'), 0)), createObject('enabled', false()))]", "service": "[if(not(empty(parameters('serviceType'))), createObject('type', parameters('serviceType')), null())]", "registries": []}, "template": {"serviceBinds": "[if(not(empty(parameters('serviceBinds'))), parameters('serviceBinds'), null())]", "containers": [{"image": "azdtemplate.azurecr.io/get-start-with-ai-agents:latest", "name": "[parameters('containerName')]", "env": "[parameters('env')]", "resources": {"cpu": "[json(parameters('containerCpuCoreCount'))]", "memory": "[parameters('containerMemory')]"}}], "scale": {"minReplicas": "[parameters('containerMinReplicas')]", "maxReplicas": "[parameters('containerMaxReplicas')]"}}}}], "outputs": {"defaultDomain": {"type": "string", "value": "[reference(resourceId('Microsoft.App/managedEnvironments', parameters('containerAppsEnvironmentName')), '2023-05-01').defaultDomain]"}, "identityPrincipalId": {"type": "string", "value": "[if(equals(variables('normalizedIdentityType'), 'None'), '', if(empty(parameters('identityName')), reference(resourceId('Microsoft.App/containerApps', parameters('name')), '2023-05-02-preview', 'full').identity.principalId, reference(resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', parameters('identityName')), '2023-01-31').principalId))]"}, "name": {"type": "string", "value": "[parameters('name')]"}, "serviceBind": {"type": "object", "value": "[if(not(empty(parameters('serviceType'))), createObject('serviceId', resourceId('Microsoft.App/containerApps', parameters('name')), 'name', parameters('name')), createObject())]"}, "uri": {"type": "string", "value": "[if(parameters('ingressEnabled'), format('https://{0}', reference(resourceId('Microsoft.App/containerApps', parameters('name')), '2023-05-02-preview').configuration.ingress.fqdn), '')]"}}}}}], "outputs": {"defaultDomain": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', format('{0}-update', deployment().name)), '2022-09-01').outputs.defaultDomain.value]"}, "name": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', format('{0}-update', deployment().name)), '2022-09-01').outputs.name.value]"}, "uri": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', format('{0}-update', deployment().name)), '2022-09-01').outputs.uri.value]"}}}}, "dependsOn": ["[resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', parameters('identityName'))]"]}], "outputs": {"SERVICE_API_IDENTITY_PRINCIPAL_ID": {"type": "string", "value": "[reference(resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', parameters('identityName')), '2023-01-31').principalId]"}, "SERVICE_API_NAME": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'container-app-module'), '2022-09-01').outputs.name.value]"}, "SERVICE_API_URI": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'container-app-module'), '2022-09-01').outputs.uri.value]"}}}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai')]", "[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'container-apps')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "user-role-acr-push", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "8311e382-0749-4cb8-b61a-304f252e45ec"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "user-role-acr-pull", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "7f951dda-4ed3-4680-a7ca-43fe172d538d"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "user-role-data-scientist", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "f6c7c914-8db3-469d-8ca1-694a8f32e121"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "user-role-secrets-reader", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "ea01e6af-a1c1-4350-9563-ad00f8c72ec5"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "user-role-azureai-developer", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "64702f94-c441-49e6-a78b-ef80e0188fee"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"condition": "[parameters('useSearchService')]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "backend-role-azure-index-data-contributor-rg", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "8ebe5a00-799e-43f5-93ac-243d3dce84a7"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"condition": "[parameters('useSearchService')]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "backend-role-azure-index-data-reader-rg", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "1407120a-92aa-4202-b7e9-c0e197c71c8f"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"condition": "[parameters('useSearchService')]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "backend-role-azure-search-service-contributor-rg", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "7ca78c08-252a-4471-8644-bb5ff32d4ba0"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"condition": "[parameters('useSearchService')]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "user-role-azure-index-data-contributor-rg", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "8ebe5a00-799e-43f5-93ac-243d3dce84a7"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"condition": "[parameters('useSearchService')]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "user-role-azure-index-data-reader-rg", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "1407120a-92aa-4202-b7e9-c0e197c71c8f"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"condition": "[parameters('useSearchService')]", "type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "user-role-azure-search-service-contributor-rg", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "7ca78c08-252a-4471-8644-bb5ff32d4ba0"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "backend-role-azureai-developer-rg", "resourceGroup": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"principalId": {"value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "roleDefinitionId": {"value": "64702f94-c441-49e6-a78b-ef80e0188fee"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "17716749299453150125"}, "description": "Creates a role assignment for a service principal."}, "parameters": {"principalId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "", "allowedValues": ["<PERSON><PERSON>", "ForeignGroup", "Group", "ServicePrincipal", "User", ""]}, "roleDefinitionId": {"type": "string"}}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(subscription().id, resourceGroup().id, parameters('principalId'), parameters('roleDefinitionId'))]", "properties": {"principalId": "[parameters('principalId')]", "principalType": "[parameters('principalType')]", "roleDefinitionId": "[resourceId('Microsoft.Authorization/roleDefinitions', parameters('roleDefinitionId'))]"}}]}}, "dependsOn": ["[extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api')]", "[subscriptionResourceId('Microsoft.Resources/resourceGroups', if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))))]"]}], "outputs": {"AZURE_RESOURCE_GROUP": {"type": "string", "value": "[if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))]"}, "AZURE_TENANT_ID": {"type": "string", "value": "[tenant().tenantId]"}, "AZURE_EXISTING_AIPROJECT_CONNECTION_STRING": {"type": "string", "value": "[if(empty(if(and(and(empty(parameters('aiExistingProjectConnectionString')), not(empty(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value))), contains(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value, '/')), split(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value, '/')[2], '')), parameters('aiExistingProjectConnectionString'), format('{0};{1};{2};{3}', if(and(and(empty(parameters('aiExistingProjectConnectionString')), not(empty(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value))), contains(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value, '/')), split(reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.discoveryUrl.value, '/')[2], ''), subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName'))), variables('projectName')))]"}, "AZURE_AI_AGENT_DEPLOYMENT_NAME": {"type": "string", "value": "[parameters('agentDeploymentName')]"}, "AZURE_AI_SEARCH_CONNECTION_NAME": {"type": "string", "value": "[parameters('searchConnectionName')]"}, "AZURE_AI_EMBED_DEPLOYMENT_NAME": {"type": "string", "value": "[parameters('embeddingDeploymentName')]"}, "AZURE_AI_SEARCH_INDEX_NAME": {"type": "string", "value": "[parameters('aiSearchIndexName')]"}, "AZURE_AI_SEARCH_ENDPOINT": {"type": "string", "value": "[if(not(parameters('useSearchService')), '', reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'ai'), '2022-09-01').outputs.searchServiceEndpoint.value)]"}, "AZURE_AI_EMBED_DIMENSIONS": {"type": "string", "value": "[parameters('embeddingDeploymentDimensions')]"}, "AZURE_AI_AGENT_NAME": {"type": "string", "value": "[parameters('agentName')]"}, "AZURE_EXISTING_AGENT_ID": {"type": "string", "value": "[variables('agentID')]"}, "AZURE_CONTAINER_ENVIRONMENT_NAME": {"type": "string", "value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'container-apps'), '2022-09-01').outputs.environmentName.value]"}, "SERVICE_API_IDENTITY_PRINCIPAL_ID": {"type": "string", "value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_IDENTITY_PRINCIPAL_ID.value]"}, "SERVICE_API_NAME": {"type": "string", "value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_NAME.value]"}, "SERVICE_API_URI": {"type": "string", "value": "[reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_URI.value]"}, "SERVICE_API_ENDPOINTS": {"type": "array", "value": ["[format('{0}', reference(extensionResourceId(format('/subscriptions/{0}/resourceGroups/{1}', subscription().subscriptionId, if(not(empty(parameters('resourceGroupName'))), parameters('resourceGroupName'), format('{0}{1}', variables('abbrs').resourcesResourceGroups, parameters('environmentName')))), 'Microsoft.Resources/deployments', 'api'), '2022-09-01').outputs.SERVICE_API_URI.value)]"]}}}