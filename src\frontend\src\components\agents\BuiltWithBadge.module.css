.badge {
  display: grid;
  grid-template-areas:
    'logo description'
    'logo brand';
  gap: 2px 8px;
  height: auto;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 0;
}

.logo {
  display: grid;
  grid-area: logo;
  place-items: center;
}

.description {
  grid-area: description;
  color: var(--colorNeutralForeground3);
}

.brand {
  display: flex;
  grid-area: brand;
  gap: 4px;
  align-items: center;
  color: var(--colorNeutralForeground1);
}

.badge:hover {
  & .brand,
  & .description {
    text-decoration: underline;
  }
}
