@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS variables for theme switching - matching FluentUI tokens */
:root {
  /* Background colors */
  --colorNeutralBackground1: theme('colors.light.background.1');
  --colorNeutralBackground2: theme('colors.light.background.2');
  --colorNeutralBackground3: theme('colors.light.background.3');
  --colorNeutralBackground4: theme('colors.light.background.4');

  /* Foreground colors */
  --colorNeutralForeground1: theme('colors.light.foreground.1');
  --colorNeutralForeground2: theme('colors.light.foreground.2');
  --colorNeutralForeground3: theme('colors.light.foreground.3');

  /* Brand colors */
  --colorBrandBackground: theme('colors.brand.100');
  --colorBrandBackgroundHover: theme('colors.brand.110');
  --colorBrandBackgroundPressed: theme('colors.brand.120');
  --colorBrandForeground1: theme('colors.brand.110');
  --colorBrandForeground2: theme('colors.brand.120');
  --colorBrandForegroundLink: theme('colors.brand.140');

  /* Stroke colors */
  --colorNeutralStroke1: theme('colors.light.stroke.1');
  --colorNeutralStroke2: theme('colors.light.stroke.2');
}

.dark {
  /* Background colors */
  --colorNeutralBackground1: theme('colors.dark.background.1');
  --colorNeutralBackground2: theme('colors.dark.background.2');
  --colorNeutralBackground3: theme('colors.dark.background.3');
  --colorNeutralBackground4: theme('colors.dark.background.4');

  /* Foreground colors */
  --colorNeutralForeground1: theme('colors.dark.foreground.1');
  --colorNeutralForeground2: theme('colors.dark.foreground.2');
  --colorNeutralForeground3: theme('colors.dark.foreground.3');

  /* Stroke colors */
  --colorNeutralStroke1: theme('colors.dark.stroke.1');
  --colorNeutralStroke2: theme('colors.dark.stroke.2');
}

/* Base styles to match FluentUI */
@layer base {
  body {
    @apply font-segoe text-body1;
    color: var(--colorNeutralForeground1);
    background-color: var(--colorNeutralBackground1);
    font-feature-settings: 'kern' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Component styles to match FluentUI components */
@layer components {
  .btn-primary {
    @apply inline-flex items-center justify-center px-fluent-lg py-fluent-sm text-body1 font-medium rounded-fluent;
    background-color: #160211;
    color: white;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    transition: all 0.1s ease-in-out;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: var(--colorBrandBackgroundHover);
  }

  .btn-primary:active:not(:disabled) {
    background-color: var(--colorBrandBackgroundPressed);
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-fluent-lg py-fluent-sm text-body1 font-medium rounded-fluent;
    background-color: var(--colorNeutralBackground2);
    color: var(--colorNeutralForeground1);
    border: 1px solid var(--colorNeutralStroke1);
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    transition: all 0.1s ease-in-out;
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: var(--colorNeutralBackground3);
  }

  .btn-secondary:active:not(:disabled) {
    background-color: var(--colorNeutralBackground4);
  }

  .btn-subtle {
    @apply inline-flex items-center justify-center px-fluent-lg py-fluent-sm text-body1 font-medium rounded-fluent;
    background-color: transparent;
    color: var(--colorNeutralForeground1);
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    transition: all 0.1s ease-in-out;
  }

  .btn-subtle:hover:not(:disabled) {
    background-color: var(--colorNeutralBackground2);
  }

  .btn-subtle:active:not(:disabled) {
    background-color: var(--colorNeutralBackground3);
  }
  
  .dropdown {
    @apply relative inline-block w-full;
  }

  .dropdown-trigger {
    @apply w-full px-fluent-md py-fluent-sm text-left rounded-fluent;
    background-color: var(--colorNeutralBackground1);
    color: var(--colorNeutralForeground1);
    border: 1px solid var(--colorNeutralStroke1);
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    transition: all 0.1s ease-in-out;
  }

  .dropdown-trigger:hover {
    border-color: var(--colorNeutralForeground3);
  }

  .dropdown-trigger:focus {
    border-color: #160211;
  }

  .dropdown-menu {
    @apply absolute z-50 w-full mt-1 rounded-fluent shadow-lg max-h-60 overflow-auto;
    background-color: var(--colorNeutralBackground1);
    border: 1px solid var(--colorNeutralStroke1);
  }

  .dropdown-item {
    @apply px-fluent-md py-fluent-sm text-body1 cursor-pointer focus:outline-none;
    color: var(--colorNeutralForeground1);
  }

  .dropdown-item:hover,
  .dropdown-item:focus {
    background-color: var(--colorNeutralBackground2);
  }
  
  .spinner {
    @apply inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent;
  }
  
  .spinner-small {
    @apply w-4 h-4;
  }
  
  .spinner-medium {
    @apply w-6 h-6;
  }
  
  .spinner-large {
    @apply w-8 h-8;
  }
}
