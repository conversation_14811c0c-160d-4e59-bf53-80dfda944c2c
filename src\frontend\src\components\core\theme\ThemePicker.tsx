import React, { useMemo, useState } from "react";
import { ChevronDown } from "lucide-react";
import { Theme, useThemeContext } from "./ThemeContext";

interface IDropdownItem {
  key: Theme;
  value: Theme;
  text: string;
}

export const ThemePicker: React.FC = () => {
  const { savedTheme, setTheme } = useThemeContext();
  const [isOpen, setIsOpen] = useState(false);

  const options: IDropdownItem[] = useMemo(
    () => [
      {
        key: "Light",
        value: "Light",
        text: "Light",
      },
      {
        key: "Dark",
        value: "Dark",
        text: "Dark",
      },
      {
        key: "System",
        value: "System",
        text: "System",
      },
    ],
    []
  );

  const selectedThemeText = useMemo(
    () =>
      options.find((opt) => opt.key === (savedTheme ?? "Light"))?.text ??
      "Light",
    [savedTheme, options]
  );

  const handleOptionSelect = (value: Theme) => {
    setTheme(value);
    setIsOpen(false);
  };

  return (
    <div className="space-y-2">
      <label htmlFor="ThemePickerDropdown" className="block text-sm font-medium">
        Theme
      </label>
      <div className="dropdown">
        <button
          id="ThemePickerDropdown"
          type="button"
          className="dropdown-trigger flex items-center justify-between"
          onClick={() => setIsOpen(!isOpen)}
          aria-expanded={isOpen}
          aria-haspopup="listbox"
        >
          <span>{selectedThemeText}</span>
          <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {isOpen && (
          <div className="dropdown-menu" role="listbox">
            {options.map((option) => (
              <button
                key={option.key}
                type="button"
                className="dropdown-item w-full text-left"
                onClick={() => handleOptionSelect(option.value)}
                role="option"
                aria-selected={option.value === savedTheme}
              >
                {option.text}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
