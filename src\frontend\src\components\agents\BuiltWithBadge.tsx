import { ReactNode, useEffect, useState } from "react";
import { ArrowRight } from "lucide-react";
import clsx from "clsx";

import { AIFoundryLogo } from "../icons/AIFoundryLogo";
import styles from "./BuiltWithBadge.module.css";

interface AzureConfig {
  subscriptionId: string;
  tenantId: string;
  resourceGroup: string;
  resourceName: string;
  projectName: string;
  wsid: string;
}

export function BuiltWithBadge({
  className,
}: {
  className?: string;
}): ReactNode {
  const [azureConfig, setAzureConfig] = useState<AzureConfig | null>(null);

  useEffect(() => {
    const fetchAzureConfig = async () => {
      try {
        const response = await fetch("/config/azure");
        if (response.ok) {
          const config = await response.json();
          setAzureConfig(config);
        } else {
          console.error("Failed to fetch Azure configuration");
        }
      } catch (error) {
        console.error("Error fetching Azure configuration:", error);
      }
    };

    fetchAzureConfig();
  }, []);

  const handleClick = () => {
    if (azureConfig) {
      const { wsid, tenantId } = azureConfig;
      const azureAiUrl = `https://ai.azure.com/resource/agentsList?wsid=${encodeURIComponent(
        wsid
      )}&tid=${tenantId}`;
      window.open(azureAiUrl, "_blank");
    } else {
      console.log("Azure configuration not available");
    }
  };
  return (
    <button
      className={clsx(styles.badge, className)}
      onClick={handleClick}
      type="button"
    >
      {" "}
      <span className={styles.logo}>
        {/* Azure AI Foundry logo */}
        <AIFoundryLogo />
      </span>
      <span className={`${styles.description} text-caption1 font-semibold`}>
        Build & deploy AI agents with
      </span>
      <span className={`${styles.brand} text-caption1 font-semibold`}>
        Azure AI Foundry <ArrowRight className="w-4 h-4 inline" aria-hidden={true} />
      </span>
    </button>
  );
}
