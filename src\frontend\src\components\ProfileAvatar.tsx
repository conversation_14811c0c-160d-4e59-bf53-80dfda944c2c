import React from 'react';
import styles from './ProfileAvatar.module.css';

interface ProfileAvatarProps {
  src?: string;
  alt?: string;
  onClick?: () => void;
}

const ProfileAvatar: React.FC<ProfileAvatarProps> = ({ 
  src = 'https://placehold.co/60x60/160211/ffffff?text=U',
  alt = 'User Avatar',
  onClick 
}) => {
  return (
    <img 
      className={styles.avatar}
      src={src}
      alt={alt}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      } : undefined}
    />
  );
};

export default ProfileAvatar;
