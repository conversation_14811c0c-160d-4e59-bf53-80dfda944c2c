import type { JSX } from "react";
import { X } from "lucide-react";

import styles from "./SettingsPanel.module.css";
import { ThemePicker } from "./theme/ThemePicker";

export interface ISettingsPanelProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export function SettingsPanel({
  isOpen = false,
  onOpenChange,
}: ISettingsPanelProps): JSX.Element {
  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => onOpenChange(false)}
        />
      )}

      {/* Drawer */}
      <div
        className={`${styles.panel} fixed top-0 right-0 h-full z-50 p-4 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{
          backgroundColor: 'var(--colorNeutralBackground1)',
          borderLeft: '1px solid var(--colorNeutralStroke1)'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b" style={{ borderColor: 'var(--colorNeutralStroke1)' }}>
          <h2 className="text-lg font-semibold" style={{ color: 'var(--colorNeutralForeground1)' }}>
            Settings
          </h2>
          <button
            className="btn-subtle p-2"
            onClick={() => onOpenChange(false)}
            aria-label="Close settings"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Body */}
        <div className={`${styles.content} mt-4`}>
          <ThemePicker />
        </div>
      </div>
    </>
  );
}
