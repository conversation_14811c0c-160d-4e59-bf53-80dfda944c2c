.avatar {
  position: fixed;
  left: 20px;
  bottom: 18px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  z-index: 1000;
}

.avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.avatar:focus-visible {
  outline: 2px solid var(--ink, #160211);
  outline-offset: 2px;
}

.avatar:active {
  transform: scale(1.05);
}

@media (prefers-reduced-motion: reduce) {
  .avatar {
    transition: none;
  }
}
