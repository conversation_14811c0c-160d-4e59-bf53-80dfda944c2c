{"name": "get-started-with-ai-agents", "image": "mcr.microsoft.com/devcontainers/python:3.11-bullseye", "forwardPorts": [50505], "features": {"ghcr.io/devcontainers/features/azure-cli:1.2.7": {}, "ghcr.io/azure/azure-dev/azd:latest": {}}, "customizations": {"vscode": {"extensions": ["ms-azuretools.azure-dev", "ms-azuretools.vscode-bicep", "ms-python.python", "ms-toolsai.jupyter", "GitHub.vscode-github-actions"]}}, "postCreateCommand": "python3 -m pip install -e src", "remoteUser": "vscode", "hostRequirements": {"memory": "8gb"}}