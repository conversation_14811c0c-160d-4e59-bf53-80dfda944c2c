import React from "react";
import { <PERSON><PERSON>erRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "./core/theme/ThemeProvider";
import Landing from "./Landing";
import Chat from "./Chat";
import Agents from "./Agents";

const App: React.FC = () => {
  return (
    <ThemeProvider>
      <Router>
        <Routes>
          <Route path="/" element={<Landing />} />
          <Route path="/chat" element={<Chat />} />
          <Route path="/chat/:sessionId" element={<Chat />} />
          <Route path="/agents" element={<Agents />} />
        </Routes>
      </Router>
    </ThemeProvider>
  );
};

export default App;
