/* ===== Modal Backdrop ===== */
.modalBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== Center Card ===== */
.center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
  width: min(500px, 90vw);
}

.card {
  position: relative;
  width: 100%;
  background: var(--card, #ffffff);
  border-radius: 12px;
  box-shadow: var(--shadow, 0 10px 30px rgba(22,2,17,.08));
  border: 1px solid rgba(22, 2, 17, .04);
  padding: 30px 40px;
  text-align: center;
}

/* ===== Close Button ===== */
.closeButton {
  position: absolute;
  top: 16px;
  right: 20px;
  background: none;
  border: none;
  color: var(--ink-80, rgba(22,2,17,.8));
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: color 0.2s ease, background-color 0.2s ease;
  font-family: inherit;
}

.closeButton:hover {
  color: var(--ink, #160211);
  background-color: rgba(22, 2, 17, 0.05);
}

.closeButton:focus-visible {
  outline: 2px solid var(--ink, #160211);
  outline-offset: 2px;
}

.title {
  margin: 5px 0 25px;
  font-size: 24px;
  color: var(--ink, #160211);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
}

.titleIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 62px;
  height: 62px;
  border-radius: 50%;
  background: var(--ink, #160211);
  flex-shrink: 0;
}

.titleIcon svg {
  width: 60px;
  height: 60px;
}

.description { 
  margin: 0 0 30px; 
  font-size: 14px; 
  color: var(--ink-80, rgba(22,2,17,.8)); 
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 300px;
  height: 40px;
  border-radius: 32px;
  border: 1px solid var(--ring, rgba(255,255,255,.9));
  background: var(--ink, #160211);
  color: #fff;
  font-size: 14px;
  text-decoration: none;
  transition: transform .08s ease, filter .2s ease, opacity .2s ease;
  cursor: pointer;
  font-family: inherit;
  padding: 0 20px;
}

.buttonText {
  flex: 1;
}

.buttonIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.buttonIcon svg {
  width: 20px;
  height: 20px;
}

.btn:hover { 
  transform: translateY(-1px); 
  filter: brightness(1.1); 
}

.btn:active { 
  transform: translateY(0); 
}

.btn:focus-visible {
  outline: 2px solid var(--ink, #160211);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .btn { 
    transition: none; 
  }
}
