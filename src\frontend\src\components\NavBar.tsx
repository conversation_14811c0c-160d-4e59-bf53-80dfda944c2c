import React from 'react';
import { Link } from 'react-router-dom';
import styles from './NavBar.module.css';

interface NavLink {
  label: string;
  href: string;
  isExternal?: boolean;
}

interface NavBarProps {
  links?: NavLink[];
  showStartChat?: boolean;
  startChatText?: string;
  startChatHref?: string;
  currentPage?: string;
}

const NavBar: React.FC<NavBarProps> = ({
  links = [
    { label: 'Home', href: '/' },
    { label: 'About', href: '#' },
    { label: 'Contact', href: '#' },
  ],
  showStartChat = true,
  startChatText = 'Start Chat',
  startChatHref = '/chat',
  currentPage,
}) => {
  return (
    <div className={styles.navWrap}>
      <nav className={styles.nav} aria-label="Primary">
        {links.map((link, index) => {
          const isActive = currentPage === link.href;
          
          if (link.isExternal) {
            return (
              <a 
                key={index}
                href={link.href}
                className={isActive ? styles.active : ''}
              >
                {link.label}
              </a>
            );
          }
          
          return (
            <Link 
              key={index}
              to={link.href}
              className={isActive ? styles.active : ''}
            >
              {link.label}
            </Link>
          );
        })}
        
        <div className={styles.spacer} aria-hidden="true"></div>
        
        {showStartChat && (
          <Link className={styles.start} to={startChatHref}>
            <span>{startChatText}</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none" aria-hidden="true">
              <path d="M17.4006 12.2371L0.679739 3.85364C0.611776 3.81966 0.533818 3.81166 0.459858 3.82965C0.377774 3.84995 0.307074 3.90194 0.263241 3.97424C0.219407 4.04655 0.206011 4.13328 0.225985 4.21544L1.94905 11.2556C1.97503 11.3616 2.05299 11.4475 2.15694 11.4815L5.10933 12.4949L2.15893 13.5084C2.05499 13.5444 1.97703 13.6283 1.95305 13.7343L0.225985 20.7844C0.207995 20.8584 0.215991 20.9364 0.249972 21.0023C0.32793 21.1602 0.519826 21.2242 0.679739 21.1462L17.4006 12.8108C17.4626 12.7808 17.5126 12.7288 17.5446 12.6688C17.6225 12.5089 17.5586 12.317 17.4006 12.2371ZM2.19691 18.7815L3.20237 14.6718L9.10316 12.6469C9.14913 12.6309 9.18711 12.5949 9.2031 12.5469C9.23109 12.463 9.18711 12.373 9.10316 12.343L3.20237 10.3201L2.20091 6.22635L14.7541 12.5209L2.19691 18.7815Z" fill="white"/>
            </svg>
          </Link>
        )}
      </nav>
    </div>
  );
};

export default NavBar;
