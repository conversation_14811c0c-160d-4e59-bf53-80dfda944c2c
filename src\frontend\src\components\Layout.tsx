import React from 'react';
import NavBar from './NavBar';
import styles from './Layout.module.css';

interface LayoutProps {
  children: React.ReactNode;
  currentPage?: string;
  showNavBar?: boolean;
}

const Layout: React.FC<LayoutProps> = ({ 
  children, 
  currentPage = '/',
  showNavBar = true 
}) => {
  return (
    <main className={styles.stage}>
      {/* Background layers */}
      <div className={styles.wash} aria-hidden="true"></div>
      <div className={`${styles.blob} ${styles.pink}`} aria-hidden="true"></div>
      <div className={`${styles.blob} ${styles.blue}`} aria-hidden="true"></div>

      {/* Navigation */}
      {showNavBar && (
        <NavBar 
          currentPage={currentPage}
          links={[
            { label: 'Home', href: '/' },
            { label: 'Agents', href: '/agents' },
            { label: 'About', href: '#' },
            { label: 'Contact', href: '#' },
          ]}
          showStartChat={true}
          startChatText="Start Chat"
          startChatHref="/chat"
        />
      )}

      {/* Page Content */}
      {children}
    </main>
  );
};

export default Layout;
