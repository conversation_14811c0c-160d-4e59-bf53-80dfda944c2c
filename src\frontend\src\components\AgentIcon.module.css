.agentIcon {
  display: inline-block;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-radius: 50%;
  outline: none;
}

.agentIcon:hover {
  transform: translateY(-2px);
}

.agentIcon:focus-visible {
  outline: 2px solid var(--ink, #160211);
  outline-offset: 4px;
}

.agentIcon:active {
  transform: translateY(0);
}

.iconContainer {
  width: 80px;
  height: 80px;
  background: var(--ink, #160211);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(22, 2, 17, 0.15);
  transition: box-shadow 0.2s ease;
}

.agentIcon:hover .iconContainer {
  box-shadow: 0 8px 30px rgba(22, 2, 17, 0.25);
}

.iconContainer svg {
  width: 36px;
  height: 36px;
}

@media (prefers-reduced-motion: reduce) {
  .agentIcon {
    transition: none;
  }
  
  .iconContainer {
    transition: none;
  }
}
