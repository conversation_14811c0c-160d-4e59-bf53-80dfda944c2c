import React from "react";

interface SuggestionBoxesProps {
  onSuggestionClick: (suggestion: string) => void;
  isVisible: boolean;
}

export const SuggestionBoxes: React.FC<SuggestionBoxesProps> = ({
  onSuggestionClick,
  isVisible,
}) => {
  const suggestions = [
    "How can you help me?",
    "What are your capabilities?",
    "Tell me about yourself"
  ];

  if (!isVisible) {
    return null;
  }

  return (
    <div>
      <div className="px-3 mb-4" style={{color: '#56637E'}}>Suggestions on what to ask your Assistant</div>

      <div className="flex flex-row gap-3 mb-4 px-3">
        {suggestions.map((suggestion, index) => (
          <button
            key={index}
            onClick={() => onSuggestionClick(suggestion)}
            className="text-left transition-all duration-200 hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex-1"
            style={{
              padding: '10px',
              background: 'rgba(255, 255, 255, 0.50)',
              borderRadius: '8px',
              outline: '1px white solid',
              outlineOffset: '-1px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              display: 'inline-flex',
              border: 'none',
              cursor: 'pointer',
              minWidth: 0
            }}
          >
            <div style={{ flex: '1 1 0' }}>
              <div style={{
                color: '#160211',
                fontSize: '14px',
                fontFamily: 'Manrope, sans-serif',
                fontWeight: '400',
                wordWrap: 'break-word'
              }}>
                {suggestion}
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default SuggestionBoxes;
