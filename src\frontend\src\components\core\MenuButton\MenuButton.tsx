/* eslint-disable react/jsx-props-no-spreading -- This is a wrapper component */
import type { Key, <PERSON><PERSON><PERSON>, MouseEventHandler, ReactNode } from "react";
import clsx from "clsx";
import { forwardRef, useCallback, useState } from "react";

import styles from "./MenuButton.module.css";

export interface IMenuItemConfig {
  key: Key;
  children?: ReactNode;
  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
  disabled?: boolean;
  className?: string;
}

export interface IMenuButtonProps {
  menuButtonText: string | React.ReactNode;
  menuItems: IMenuItemConfig[];
  className?: string;
  appearance?: "primary" | "secondary" | "subtle";
  shape?: "rounded" | "circular" | "square";
  onClick?: MouseEventHandler<HTMLButtonElement>;
}

export const MenuButton = forwardRef<HTMLButtonElement, IMenuButtonProps>(
  (
    {
      menuButtonText,
      menuItems,
      className,
      appearance = "secondary",
      shape = "rounded",
      onClick,
    },
    ref
  ): ReactNode => {
    const [isOpen, setIsOpen] = useState(false);

    const handleMenuButtonClick = useCallback(
      (event: MouseEvent<HTMLButtonElement>) => {
        setIsOpen(!isOpen);
        if (onClick) {
          onClick(event);
        }
      },
      [isOpen, onClick]
    );

    const handleMenuItemClick = useCallback(
      (
        event: MouseEvent<HTMLButtonElement>,
        itemOnClick?: (event: MouseEvent<HTMLButtonElement>) => void
      ) => {
        setIsOpen(false);
        if (itemOnClick) {
          itemOnClick(event);
        }
      },
      []
    );

    const buttonClasses = clsx(
      appearance === "primary" && "btn-primary",
      appearance === "secondary" && "btn-secondary",
      appearance === "subtle" && "btn-subtle",
      appearance === "secondary" && styles.secondary,
      shape === "circular" && "rounded-full",
      shape === "square" && "rounded-none",
      className
    );

    return (
      <div className="relative inline-block">
        <button
          ref={ref}
          type="button"
          className={buttonClasses}
          onClick={handleMenuButtonClick}
          aria-expanded={isOpen}
          aria-haspopup="menu"
        >
          {menuButtonText}
        </button>

        {isOpen && (
          <>
            {/* Backdrop to close menu when clicking outside */}
            <div
              className="fixed inset-0 z-10"
              onClick={() => setIsOpen(false)}
            />

            {/* Menu */}
            <div className="absolute right-0 mt-1 z-20 min-w-48 rounded-fluent shadow-lg" style={{
              backgroundColor: 'var(--colorNeutralBackground1)',
              border: '1px solid var(--colorNeutralStroke1)'
            }}>
              {menuItems.map(({ key, children, onClick: itemOnClick, disabled, className: itemClassName }) => (
                <button
                  key={key}
                  type="button"
                  className={clsx(
                    "w-full px-fluent-md py-fluent-sm text-left text-body1 hover:bg-opacity-10 focus:outline-none",
                    disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer",
                    itemClassName
                  )}
                  style={{
                    color: 'var(--colorNeutralForeground1)',
                  }}
                  onClick={(event) => {
                    if (!disabled) {
                      handleMenuItemClick(event, itemOnClick);
                    }
                  }}
                  disabled={disabled}
                  onMouseEnter={(e) => {
                    if (!disabled) {
                      e.currentTarget.style.backgroundColor = 'var(--colorNeutralBackground2)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  {children}
                </button>
              ))}
            </div>
          </>
        )}
      </div>
    );
  }
);
MenuButton.displayName = "MenuButton";
