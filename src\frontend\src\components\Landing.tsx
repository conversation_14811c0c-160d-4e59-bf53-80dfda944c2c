import React from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from './Layout';
import ActionCard from './ActionCard';

const Landing: React.FC = () => {
  const navigate = useNavigate();

  const handleStartChat = () => {
    navigate('/agents');
  };

  return (
    <Layout currentPage="/">
      {/* Center Action Card */}
      <ActionCard
        title="Hi there!"
        description="Sign in to get smarter answers!"
        buttonText="Login"
        onButtonClick={handleStartChat}
      />
    </Layout>
  );
};

export default Landing;
