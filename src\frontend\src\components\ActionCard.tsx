import React, { useEffect } from 'react';
import styles from './ActionCard.module.css';

interface ActionCardProps {
  title: string;
  description: string;
  buttonText: string;
  onButtonClick: () => void;
  className?: string;
  showCloseButton?: boolean;
  onClose?: () => void;
  buttonIcon?: React.ReactNode;
  titleIcon?: React.ReactNode;
  isModal?: boolean;
}

const ActionCard: React.FC<ActionCardProps> = ({
  title,
  description,
  buttonText,
  onButtonClick,
  className = '',
  showCloseButton = false,
  onClose,
  buttonIcon,
  titleIcon,
  isModal = false,
}) => {
  // Handle escape key press for modal
  useEffect(() => {
    if (!isModal || !onClose) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isModal, onClose]);

  // Handle backdrop click
  const handleBackdropClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget && onClose) {
      onClose();
    }
  };

  const cardContent = (
    <div
      className={styles.card}
      role="dialog"
      aria-labelledby="action-card-title"
      aria-describedby="action-card-desc"
      aria-modal={isModal}
    >
      {/* Close Button */}
      {showCloseButton && onClose && (
        <button
          className={styles.closeButton}
          onClick={onClose}
          type="button"
          aria-label="Close modal"
        >
          Close
        </button>
      )}

      {/* Title with optional icon */}
      <h1 id="action-card-title" className={styles.title}>
        {titleIcon && <span className={styles.titleIcon}>{titleIcon}</span>}
        {title}
      </h1>

      <p id="action-card-desc" className={styles.description}>
        {description}
      </p>

      {/* Button with optional icon */}
      <button
        className={styles.btn}
        onClick={onButtonClick}
        type="button"
      >
        <span className={styles.buttonText}>{buttonText}</span>
        {buttonIcon && <span className={styles.buttonIcon}>{buttonIcon}</span>}
      </button>
    </div>
  );

  if (isModal) {
    return (
      <div className={styles.modalBackdrop} onClick={handleBackdropClick}>
        <section className={`${styles.center} ${className}`}>
          {cardContent}
        </section>
      </div>
    );
  }

  return (
    <section className={`${styles.center} ${className}`}>
      {cardContent}
    </section>
  );
};

export default ActionCard;
