import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { AgentPreview } from "./agents/AgentPreview";
import Layout from "./Layout";
import ChatSidebar from "./ChatSidebar";
import ProfileMenu from "./ProfileMenu";
import { SettingsPanel } from "./core/SettingsPanel";

const Chat: React.FC = () => {
  // Router hooks for URL parameters and navigation
  const { sessionId } = useParams<{ sessionId?: string }>();
  const navigate = useNavigate();

  // State to store the agent details
  const [agentDetails, setAgentDetails] = useState({
    id: "loading",
    object: "agent",
    created_at: Date.now(),
    name: "Loading...",
    description: "Loading agent details...",
    model: "default",
    metadata: {
      logo: "Avatar_Default.svg",
    },
  });

  // State for sidebar
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // State for settings panel
  const [isSettingsPanelOpen, setIsSettingsPanelOpen] = useState(false);

  // State for tracking which chat session is currently active
  // Will be set by useEffect based on URL parameter
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);

  // State to trigger chat refresh (now handled by URL changes)
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // State for new chat creation loading
  const [isCreatingNewChat, setIsCreatingNewChat] = useState(false);

  // Helper function to clear session cookies
  const clearSessionCookies = () => {
    const cookiesToClear = ['session_id', 'thread_id', 'agent_id', 'user_id'];
    cookiesToClear.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    });
    console.log('Session cookies cleared for new chat');
  };

  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleSessionSelect = async (sessionId: string) => {
    try {
      // Switch to the selected session
      const response = await fetch(`/chat/sessions/${sessionId}/switch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (response.ok) {
        // Navigate to the session URL instead of just updating state
        navigate(`/chat/${sessionId}`);
        setIsSidebarOpen(false); // Close sidebar after selection
      } else {
        console.error('Failed to switch to session');
      }
    } catch (error) {
      console.error('Error switching to session:', error);
    }
  };

  const handleNewChat = async () => {
    try {
      setIsCreatingNewChat(true);

      // Clear session cookies to ensure fresh start
      clearSessionCookies();

      // Don't create session immediately - just navigate to /chat
      // Session will be created lazily when first message is sent
      navigate('/chat');
      setIsSidebarOpen(false); // Close sidebar after creating new chat
    } catch (error) {
      console.error('Error starting new chat:', error);
    } finally {
      setIsCreatingNewChat(false);
    }
  };

  // Initialize session based on URL parameter or create new session
  useEffect(() => {
    const initializeCurrentSession = async () => {
      if (sessionId) {
        // If URL has session ID, verify it exists and belongs to user
        try {
          const response = await fetch(`/chat/sessions/${sessionId}/messages`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          });

          if (response.ok) {
            // Session exists and is accessible, use it
            setCurrentSessionId(sessionId);
          } else {
            // Session doesn't exist or not accessible, redirect to new chat
            navigate('/chat');
          }
        } catch (error) {
          console.error('Error verifying session:', error);
          navigate('/chat');
        }
      } else {
        // No session ID in URL, this means we want a new chat
        // Don't auto-load any existing session - start fresh
        setCurrentSessionId(null);
      }
    };

    initializeCurrentSession();
  }, [sessionId, navigate]); // Re-run when URL parameter changes

  // Update currentSessionId when URL parameter changes
  useEffect(() => {
    setCurrentSessionId(sessionId || null);
  }, [sessionId]);

  // Handle new session creation
  const handleSessionCreated = (newSessionId: string) => {
    console.log('New session created:', newSessionId);
    // Navigate to the new session URL
    navigate(`/chat/${newSessionId}`);
    // Trigger sidebar refresh to show new session
    setRefreshTrigger(prev => prev + 1);
  };

  // Fetch agent details when component mounts
  useEffect(() => {
    const fetchAgentDetails = async () => {
      try {
        const response = await fetch("/agent", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          console.log(
            "Agent details fetched successfully:",
            JSON.stringify(data)
          );
          console.log(
            "Agent details fetched successfully 2:",
            JSON.stringify(response)
          );
          setAgentDetails(data);
        } else {
          console.error("Failed to fetch agent details");
          // Set fallback data if fetch fails
          setAgentDetails({
            id: "fallback",
            object: "agent",
            created_at: Date.now(),
            name: "AI Agent",
            description: "Could not load agent details",
            model: "default",
            metadata: {
              logo: "Avatar_Default.svg",
            },
          });
        }
      } catch (error) {
        console.error("Error fetching agent details:", error);
        // Set fallback data if fetch fails
        setAgentDetails({
          id: "error",
          object: "agent",
          created_at: Date.now(),
          name: "AI Agent",
          description: "Error loading agent details",
          model: "default",
          metadata: {
            logo: "Avatar_Default.svg",
          },
        });
      }
    };

    fetchAgentDetails();
  }, []);

  // Menu items for profile dropdown
  const profileMenuItems = [
    {
      key: "settings",
      children: "Settings",
      onClick: () => {
        setIsSettingsPanelOpen(true);
      },
    },
    {
      key: "terms",
      children: (
        <a
          href="https://aka.ms/aistudio/terms"
          target="_blank"
          rel="noopener noreferrer"
          style={{ color: 'inherit', textDecoration: 'none' }}
        >
          Terms of Use
        </a>
      ),
    },
    {
      key: "logout",
      children: (
        <a
          href="/"
          style={{ color: 'inherit', textDecoration: 'none' }}
        >
          Logout
        </a>
      ),
    },
  ];

  return (
    <Layout currentPage="/chat" showNavBar={false}>
      <ChatSidebar
        isOpen={isSidebarOpen}
        onToggle={handleSidebarToggle}
        onSessionSelect={handleSessionSelect}
        onNewChat={handleNewChat}
        currentSessionId={currentSessionId}
        refreshTrigger={refreshTrigger}
      />
      <AgentPreview
        resourceId="sample-resource-id"
        agentDetails={agentDetails}
        onSidebarToggle={handleSidebarToggle}
        refreshTrigger={refreshTrigger}
        currentSessionId={currentSessionId}
        onSessionCreated={handleSessionCreated}
        isCreatingNewChat={isCreatingNewChat}
      />

      {/* Profile Menu */}
      <ProfileMenu
        menuItems={profileMenuItems}
        alt="User Profile"
      />

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={isSettingsPanelOpen}
        onOpenChange={setIsSettingsPanelOpen}
      />
    </Layout>
  );
};

export default Chat;
