# Copyright (c) Microsoft. All rights reserved.
# Licensed under the MIT license. See LICENSE.md file in the project root for full license information.

import asyncio
import json
import os
import re
from typing import AsyncGenerator, Optional, Dict, List

import fastapi
from fastapi import Request, Depends, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse, StreamingResponse
from fastapi.templating import Jinja2Templates
from fastapi.responses import JSONResponse

import logging
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator

from .cosmos_db import cosmos_manager, ChatSession, ChatMessage

from azure.ai.agents.aio import AgentsClient
from azure.ai.agents.models import (
    Agent,
    MessageDeltaChunk,
    ThreadMessage,
    ThreadRun,
    AsyncAgentEventHandler,
    RunStep,
    ListSortOrder
)
from azure.ai.projects import AIProjectClient
from azure.ai.projects.models import (
   AgentEvaluationRequest,
   AgentEvaluationSamplingConfiguration,
   AgentEvaluationRedactionConfiguration,
   EvaluatorIds
)


# Create a logger for this module
logger = logging.getLogger("azureaiapp")

# Set the log level for the azure HTTP logging policy to WARNING (or ERROR)
logging.getLogger("azure.core.pipeline.policies.http_logging_policy").setLevel(logging.WARNING)

from opentelemetry import trace
tracer = trace.get_tracer(__name__)

# Define the directory for your templates.
directory = os.path.join(os.path.dirname(__file__), "templates")
templates = Jinja2Templates(directory=directory)

# Create a new FastAPI router
router = fastapi.APIRouter()

from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from typing import Optional
import secrets

security = HTTPBasic()

username = os.getenv("WEB_APP_USERNAME")
password = os.getenv("WEB_APP_PASSWORD")
basic_auth = username and password

def authenticate(credentials: Optional[HTTPBasicCredentials] = Depends(security)) -> None:

    if not basic_auth:
        logger.info("Skipping authentication: WEB_APP_USERNAME or WEB_APP_PASSWORD not set.")
        return
    
    correct_username = secrets.compare_digest(credentials.username, username)
    correct_password = secrets.compare_digest(credentials.password, password)
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    return

auth_dependency = Depends(authenticate) if basic_auth else None


def get_ai_project(request: Request) -> AIProjectClient:
    return request.app.state.ai_project

def get_agent_client(request: Request) -> AgentsClient:
    return request.app.state.agent_client

def get_agent(request: Request) -> Agent:
    return request.app.state.agent

def get_app_insights_conn_str(request: Request) -> str:
    if hasattr(request.app.state, "application_insights_connection_string"):
        return request.app.state.application_insights_connection_string
    else:
        return None

def serialize_sse_event(data: Dict) -> str:
    return f"data: {json.dumps(data)}\n\n"

async def get_message_and_annotations(agent_client : AgentsClient, message: ThreadMessage) -> Dict:
    annotations = []
    # Get file annotations for the file search.
    for annotation in (a.as_dict() for a in message.file_citation_annotations):
        file_id = annotation["file_citation"]["file_id"]
        logger.info(f"Fetching file with ID for annotation {file_id}")
        openai_file = await agent_client.files.get(file_id)
        annotation["file_name"] = openai_file.filename
        logger.info(f"File name for annotation: {annotation['file_name']}")
        annotations.append(annotation)

    # Get url annotation for the index search.
    for url_annotation in message.url_citation_annotations:
        annotation = url_annotation.as_dict()
        annotation["file_name"] = annotation['url_citation']['title']
        logger.info(f"File name for annotation: {annotation['file_name']}")
        annotations.append(annotation)
            
    return {
        'content': message.text_messages[0].text.value,
        'annotations': annotations
    }

class MyEventHandler(AsyncAgentEventHandler[str]):
    def __init__(self, ai_project: AIProjectClient, app_insights_conn_str: str):
        super().__init__()
        self.agent_client = ai_project.agents
        self.ai_project = ai_project
        self.app_insights_conn_str = app_insights_conn_str

    async def on_message_delta(self, delta: MessageDeltaChunk) -> Optional[str]:
        stream_data = {'content': delta.text, 'type': "message"}
        return serialize_sse_event(stream_data)

    async def on_thread_message(self, message: ThreadMessage) -> Optional[str]:
        try:
            logger.info(f"MyEventHandler: Received thread message, message ID: {message.id}, status: {message.status}")
            if message.status != "completed":
                return None

            logger.info("MyEventHandler: Received completed message")

            stream_data = await get_message_and_annotations(self.agent_client, message)
            stream_data['type'] = "completed_message"
            return serialize_sse_event(stream_data)
        except Exception as e:
            logger.error(f"Error in event handler for thread message: {e}", exc_info=True)
            return None

    async def on_thread_run(self, run: ThreadRun) -> Optional[str]:
        logger.info("MyEventHandler: on_thread_run event received")
        run_information = f"ThreadRun status: {run.status}, thread ID: {run.thread_id}"
        stream_data = {'content': run_information, 'type': 'thread_run'}
        if run.status == "failed":
            stream_data['error'] = run.last_error.as_dict()
        # automatically run agent evaluation when the run is completed
        if run.status == "completed":
            run_agent_evaluation(run.thread_id, run.id, self.ai_project, self.app_insights_conn_str)
        return serialize_sse_event(stream_data)

    async def on_error(self, data: str) -> Optional[str]:
        logger.error(f"MyEventHandler: on_error event received: {data}")
        stream_data = {'type': "stream_end"}
        return serialize_sse_event(stream_data)

    async def on_done(self) -> Optional[str]:
        logger.info("MyEventHandler: on_done event received")
        stream_data = {'type': "stream_end"}
        return serialize_sse_event(stream_data)

    async def on_run_step(self, step: RunStep) -> Optional[str]:
        logger.info(f"Step {step['id']} status: {step['status']}")
        step_details = step.get("step_details", {})
        tool_calls = step_details.get("tool_calls", [])

        if tool_calls:
            logger.info("Tool calls:")
            for call in tool_calls:
                azure_ai_search_details = call.get("azure_ai_search", {})
                if azure_ai_search_details:
                    logger.info(f"azure_ai_search input: {azure_ai_search_details.get('input')}")
                    logger.info(f"azure_ai_search output: {azure_ai_search_details.get('output')}")
        return None

@router.get("/", response_class=HTMLResponse)
async def index(request: Request, _ = auth_dependency):
    return templates.TemplateResponse(
        "index.html",
        {
            "request": request,
        }
    )

# Specific routes for React client-side routing
@router.get("/agents", response_class=HTMLResponse)
async def agents_page(request: Request, _ = auth_dependency):
    return templates.TemplateResponse(
        "index.html",
        {
            "request": request,
        }
    )

# Note: /chat route conflicts with POST /chat API endpoint
# The React app will handle client-side routing for /chat
# We'll add a catch-all at the end for any remaining routes


async def get_result_with_session(
    request: Request,
    thread_id: str,
    agent_id: str,
    ai_project: AIProjectClient,
    app_insight_conn_str: Optional[str],
    carrier: Dict[str, str],
    session_id: str
) -> AsyncGenerator[str, None]:
    """Enhanced version that stores assistant responses in CosmosDB"""
    ctx = TraceContextTextMapPropagator().extract(carrier=carrier)
    with tracer.start_as_current_span('get_result_with_session', context=ctx):
        logger.info(f"get_result_with_session invoked for session_id={session_id}, thread_id={thread_id}")
        assistant_response_content = ""

        try:
            # Send session information as the first event
            session_info_event = serialize_sse_event({
                'type': 'session_info',
                'session_id': session_id,
                'thread_id': thread_id
            })
            yield session_info_event

            agent_client = ai_project.agents
            async with await agent_client.runs.stream(
                thread_id=thread_id,
                agent_id=agent_id,
                event_handler=MyEventHandler(ai_project, app_insight_conn_str),
            ) as stream:
                logger.info("Successfully created stream; starting to process events")
                async for event in stream:
                    _, _, event_func_return_val = event
                    logger.debug(f"Received event: {event}")
                    if event_func_return_val:
                        # Parse the event to extract content
                        try:
                            event_data = json.loads(event_func_return_val.replace("data: ", ""))
                            if event_data.get('type') == 'message' and event_data.get('content'):
                                assistant_response_content += event_data['content']
                        except:
                            pass  # Continue if parsing fails

                        logger.info(f"Yielding event: {event_func_return_val}")
                        yield event_func_return_val
                    else:
                        logger.debug("Event received but no data to yield")

                # Store the complete assistant response in CosmosDB
                if assistant_response_content.strip():
                    try:
                        assistant_message = ChatMessage(
                            session_id=session_id,
                            role="assistant",
                            content=assistant_response_content.strip()
                        )
                        await cosmos_manager.add_message(assistant_message)
                        logger.info(f"Stored assistant response in session: {session_id}")
                    except Exception as e:
                        logger.error(f"Failed to store assistant response: {e}")

        except Exception as e:
            logger.exception(f"Exception in get_result_with_session: {e}")
            yield serialize_sse_event({'type': "error", 'message': str(e)})


async def get_result(
    request: Request,
    thread_id: str,
    agent_id: str,
    ai_project: AIProjectClient,
    app_insight_conn_str: Optional[str],
    carrier: Dict[str, str]
) -> AsyncGenerator[str, None]:
    """Legacy version for backward compatibility"""
    ctx = TraceContextTextMapPropagator().extract(carrier=carrier)
    with tracer.start_as_current_span('get_result', context=ctx):
        logger.info(f"get_result invoked for thread_id={thread_id} and agent_id={agent_id}")
        try:
            agent_client = ai_project.agents
            async with await agent_client.runs.stream(
                thread_id=thread_id,
                agent_id=agent_id,
                event_handler=MyEventHandler(ai_project, app_insight_conn_str),
            ) as stream:
                logger.info("Successfully created stream; starting to process events")
                async for event in stream:
                    _, _, event_func_return_val = event
                    logger.debug(f"Received event: {event}")
                    if event_func_return_val:
                        logger.info(f"Yielding event: {event_func_return_val}")
                        yield event_func_return_val
                    else:
                        logger.debug("Event received but no data to yield")
        except Exception as e:
            logger.exception(f"Exception in get_result: {e}")
            yield serialize_sse_event({'type': "error", 'message': str(e)})


@router.get("/chat/history")
async def history(
    request: Request,
    ai_project : AIProjectClient = Depends(get_ai_project),
    agent : Agent = Depends(get_agent),
	_ = auth_dependency
):
    """Get chat history for current session. If no session exists, create a new one."""
    with tracer.start_as_current_span("chat_history"):
        user_id = get_user_id(request)
        session_id = request.cookies.get('session_id')
        thread_id = request.cookies.get('thread_id')
        agent_id = request.cookies.get('agent_id')

        # Try to get existing session or create new one
        session = None
        if session_id:
            session = await cosmos_manager.get_session(session_id)
            if session and session.user_id != user_id:
                session = None  # Session doesn't belong to this user

        # If no valid session, create a new one
        if not session:
            try:
                agent_client = ai_project.agents
                thread = await agent_client.threads.create()

                session = ChatSession(
                    user_id=user_id,
                    agent_id=agent.id,
                    thread_id=thread.id,
                    title="New Chat"
                )
                session = await cosmos_manager.create_session(session)
                thread_id = thread.id

                logger.info(f"Created new session: {session.session_id}")
            except Exception as e:
                logger.error(f"Error creating new session: {e}")
                raise HTTPException(status_code=500, detail=f"Error creating session: {e}")
        else:
            thread_id = session.thread_id

        # Get messages from Azure AI Agents
        try:
            agent_client = ai_project.agents
            content = []
            response = agent_client.messages.list(thread_id=thread_id, order=ListSortOrder.ASCENDING)

            async for message in response:
                formatteded_message = await get_message_and_annotations(agent_client, message)
                formatteded_message['role'] = message.role
                formatteded_message['created_at'] = message.created_at.astimezone().strftime("%m/%d/%y, %I:%M %p")
                content.append(formatteded_message)

            logger.info(f"Retrieved {len(content)} messages for session: {session.session_id}")
            response = JSONResponse(content=content)

            # Update cookies to persist the session and thread IDs
            response.set_cookie("session_id", session.session_id)
            response.set_cookie("thread_id", thread_id)
            response.set_cookie("agent_id", agent.id)
            response.set_cookie("user_id", user_id)
            return response

        except Exception as e:
            logger.error(f"Error listing messages: {e}")
            raise HTTPException(status_code=500, detail=f"Error listing messages: {e}")

@router.get("/agent")
async def get_chat_agent(
    request: Request
):
    return JSONResponse(content=get_agent(request).as_dict())  

@router.post("/chat")
async def chat(
    request: Request,
    agent : Agent = Depends(get_agent),
    ai_project: AIProjectClient = Depends(get_ai_project),
    app_insights_conn_str : str = Depends(get_app_insights_conn_str),
	_ = auth_dependency
):
    """Send a message in the current chat session"""
    user_id = get_user_id(request)
    session_id = request.cookies.get('session_id')
    thread_id = request.cookies.get('thread_id')
    agent_id = request.cookies.get('agent_id')

    with tracer.start_as_current_span("chat_request"):
        carrier = {}
        TraceContextTextMapPropagator().inject(carrier)

        # Parse the JSON from the request first to check for session_id override
        try:
            user_message = await request.json()
        except Exception as e:
            logger.error(f"Invalid JSON in request: {e}")
            raise HTTPException(status_code=400, detail=f"Invalid JSON in request: {e}")

        logger.info(f"user_message: {user_message}")
        message_content = user_message.get('message', '')

        # Check if session_id is provided in request body (overrides cookie)
        request_session_id = user_message.get('session_id')
        if request_session_id:
            session_id = request_session_id

        # Get or create session
        session = None
        if session_id:
            session = await cosmos_manager.get_session(session_id)
            if session and session.user_id != user_id:
                session = None  # Session doesn't belong to this user

        # If no valid session, create a new one
        if not session:
            try:
                agent_client = ai_project.agents
                thread = await agent_client.threads.create()

                session = ChatSession(
                    user_id=user_id,
                    agent_id=agent.id,
                    thread_id=thread.id,
                    title="New Chat"
                )
                session = await cosmos_manager.create_session(session)
                thread_id = thread.id

                logger.info(f"Created new session for chat: {session.session_id}")
            except Exception as e:
                logger.error(f"Error creating session: {e}")
                raise HTTPException(status_code=500, detail=f"Error creating session: {e}")
        else:
            thread_id = session.thread_id

        # Store user message in CosmosDB
        try:
            user_chat_message = ChatMessage(
                session_id=session.session_id,
                role="user",
                content=message_content
            )
            await cosmos_manager.add_message(user_chat_message)

            # Update session title if this is the first user message
            # Check if this is the first user message by counting existing messages
            existing_messages = await cosmos_manager.get_session_messages(session.session_id)
            user_messages = [msg for msg in existing_messages if msg.role == "user"]

            if len(user_messages) == 1:  # This is the first user message
                # Generate a meaningful title from the first message
                title = generate_session_title(message_content)
                session.title = title
                await cosmos_manager.update_session(session)
                logger.info(f"Updated session title to: {title}")

        except Exception as e:
            logger.error(f"Error storing user message: {e}")
            # Continue with Azure AI Agents even if CosmosDB fails

        # Create message in Azure AI Agents
        try:
            agent_client = ai_project.agents
            message = await agent_client.messages.create(
                thread_id=thread_id,
                role="user",
                content=message_content
            )
            logger.info(f"Created message in thread, message ID: {message.id}")
        except Exception as e:
            logger.error(f"Error creating message: {e}")
            raise HTTPException(status_code=500, detail=f"Error creating message: {e}")

        # Set the Server-Sent Events (SSE) response headers.
        headers = {
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
        logger.info(f"Starting streaming response for session {session.session_id}, thread ID {thread_id}")

        # Create the streaming response using the generator.
        response = StreamingResponse(
            get_result_with_session(request, thread_id, agent.id, ai_project, app_insights_conn_str, carrier, session.session_id),
            headers=headers
        )

        # Update cookies to persist the session and thread IDs
        response.set_cookie("session_id", session.session_id)
        response.set_cookie("thread_id", thread_id)
        response.set_cookie("agent_id", agent.id)
        response.set_cookie("user_id", user_id)
        return response

def read_file(path: str) -> str:
    with open(path, 'r') as file:
        return file.read()


def run_agent_evaluation(
    thread_id: str, 
    run_id: str,
    ai_project: AIProjectClient,
    app_insights_conn_str: str):

    if app_insights_conn_str:
        agent_evaluation_request = AgentEvaluationRequest(
            run_id=run_id,
            thread_id=thread_id,
            evaluators={
                "Relevance": {"Id": EvaluatorIds.RELEVANCE.value},
                "TaskAdherence": {"Id": EvaluatorIds.TASK_ADHERENCE.value},
                "ToolCallAccuracy": {"Id": EvaluatorIds.TOOL_CALL_ACCURACY.value},
            },
            sampling_configuration=AgentEvaluationSamplingConfiguration(
                name="default",
                sampling_percent=100,
            ),
            redaction_configuration=AgentEvaluationRedactionConfiguration(
                redact_score_properties=False,
            ),
            app_insights_connection_string=app_insights_conn_str,
        )
        
        async def run_evaluation():
            try:        
                logger.info(f"Running agent evaluation on thread ID {thread_id} and run ID {run_id}")
                agent_evaluation_response = await ai_project.evaluations.create_agent_evaluation(
                    evaluation=agent_evaluation_request
                )
                logger.info(f"Evaluation response: {agent_evaluation_response}")
            except Exception as e:
                logger.error(f"Error creating agent evaluation: {e}")

        # Create a new task to run the evaluation asynchronously
        asyncio.create_task(run_evaluation())


@router.get("/config/azure")
async def get_azure_config(_ = auth_dependency):
    """Get Azure configuration for frontend use"""
    try:
        subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID", "")
        tenant_id = os.getenv("AZURE_TENANT_ID", "")
        resource_group = os.getenv("AZURE_RESOURCE_GROUP", "")
        ai_project_resource_id = os.getenv("AZURE_EXISTING_AIPROJECT_RESOURCE_ID", "") or os.getenv("AZURE_AI_PROJECT_RESOURCE_ID", "")
        
        # Extract resource name and project name from the resource ID
        # Format: /subscriptions/{sub}/resourceGroups/{rg}/providers/Microsoft.CognitiveServices/accounts/{resource}/projects/{project}
        resource_name = ""
        project_name = ""
        
        if ai_project_resource_id:
            parts = ai_project_resource_id.split("/")
            if len(parts) >= 8:
                resource_name = parts[8]  # accounts/{resource_name}
            if len(parts) >= 10:
                project_name = parts[10]  # projects/{project_name}
        
        return JSONResponse({
            "subscriptionId": subscription_id,
            "tenantId": tenant_id,
            "resourceGroup": resource_group,
            "resourceName": resource_name,
            "projectName": project_name,
            "wsid": ai_project_resource_id
        })
    except Exception as e:
        logger.error(f"Error getting Azure config: {e}")
        raise HTTPException(status_code=500, detail="Failed to get Azure configuration")


# Chat Session Management Endpoints

def get_user_id(request: Request) -> str:
    """Get user ID from request. For now, use a default user ID.
    In production, this should extract from authentication token."""
    return request.cookies.get('user_id', 'default_user')


def generate_session_title(message_content: str) -> str:
    """
    Generate a meaningful title from the first user message.
    Uses smart truncation and keyword extraction.
    """
    # Clean the message content
    title = message_content.strip()

    # Remove common question words and filler phrases for more meaningful titles
    title = re.sub(r'^(hi|hello|hey|can you|could you|please|would you|i need|i want|help me|how do i|what is|what are|tell me|explain)\s+', '', title, flags=re.IGNORECASE)

    # If the cleaned title is too short, use the original
    if len(title.strip()) < 10:
        title = message_content.strip()

    # Smart truncation
    if len(title) > 50:
        # Try to find a good break point (space, punctuation) near 50 chars
        truncate_at = 47
        for i in range(47, min(len(title), 60)):
            if title[i] in ' .,!?;:-':
                truncate_at = i
                break
        title = title[:truncate_at] + "..."

    # Capitalize first letter
    if title:
        title = title[0].upper() + title[1:]

    return title or "New Chat"

@router.get("/chat/sessions")
async def get_chat_sessions(
    request: Request,
    agent_id: Optional[str] = None,
    _ = auth_dependency
):
    """Get all chat sessions for the current user"""
    try:
        user_id = get_user_id(request)
        sessions = await cosmos_manager.get_user_sessions(user_id, agent_id)

        # Convert to response format
        session_list = []
        for session in sessions:
            session_data = session.to_dict()
            # Format dates for frontend
            if session_data.get('created_at') and hasattr(session, 'created_at') and session.created_at:
                session_data['created_at'] = session.created_at.strftime("%m/%d/%y, %I:%M %p")
            if session_data.get('updated_at') and hasattr(session, 'updated_at') and session.updated_at:
                session_data['updated_at'] = session.updated_at.strftime("%m/%d/%y, %I:%M %p")
            session_list.append(session_data)

        return JSONResponse(content=session_list)

    except Exception as e:
        logger.error(f"Error getting chat sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get chat sessions")

@router.post("/chat/sessions")
async def create_chat_session(
    request: Request,
    agent: Agent = Depends(get_agent),
    ai_project: AIProjectClient = Depends(get_ai_project),
    _ = auth_dependency
):
    """Create a new chat session"""
    try:
        user_id = get_user_id(request)

        # Create a new thread for this session
        agent_client = ai_project.agents
        thread = await agent_client.threads.create()

        # Create session in CosmosDB
        session = ChatSession(
            user_id=user_id,
            agent_id=agent.id,
            thread_id=thread.id,
            title="New Chat"
        )

        created_session = await cosmos_manager.create_session(session)

        # Set cookies for the new session
        response_data = created_session.to_dict()
        response_data['created_at'] = created_session.created_at.strftime("%m/%d/%y, %I:%M %p")
        response_data['updated_at'] = created_session.updated_at.strftime("%m/%d/%y, %I:%M %p")

        response = JSONResponse(content=response_data)
        response.set_cookie("session_id", created_session.session_id)
        response.set_cookie("thread_id", thread.id)
        response.set_cookie("agent_id", agent.id)
        response.set_cookie("user_id", user_id)

        return response

    except Exception as e:
        logger.error(f"Error creating chat session: {e}")
        raise HTTPException(status_code=500, detail="Failed to create chat session")

@router.get("/chat/sessions/{session_id}/messages")
async def get_session_messages(
    session_id: str,
    request: Request,
    _ = auth_dependency
):
    """Get all messages for a specific session"""
    try:
        user_id = get_user_id(request)

        # Verify session belongs to user
        session = await cosmos_manager.get_session(session_id)
        if not session or session.user_id != user_id:
            raise HTTPException(status_code=404, detail="Session not found")

        messages = await cosmos_manager.get_session_messages(session_id)

        # Convert to response format
        message_list = []
        for message in messages:
            message_data = {
                'id': message.message_id,
                'role': message.role,
                'content': message.content,
                'created_at': message.created_at.strftime("%m/%d/%y, %I:%M %p"),
                'annotations': message.annotations
            }
            message_list.append(message_data)

        return JSONResponse(content=message_list)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session messages: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session messages")

@router.delete("/chat/sessions/{session_id}")
async def delete_chat_session(
    session_id: str,
    request: Request,
    _ = auth_dependency
):
    """Delete a chat session"""
    try:
        user_id = get_user_id(request)

        # Verify session belongs to user
        session = await cosmos_manager.get_session(session_id)
        if not session or session.user_id != user_id:
            raise HTTPException(status_code=404, detail="Session not found")

        success = await cosmos_manager.delete_session(session_id)

        if success:
            return JSONResponse(content={"message": "Session deleted successfully"})
        else:
            raise HTTPException(status_code=500, detail="Failed to delete session")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete session")

@router.post("/chat/sessions/{session_id}/switch")
async def switch_to_session(
    session_id: str,
    request: Request,
    _ = auth_dependency
):
    """Switch to a specific chat session"""
    try:
        user_id = get_user_id(request)

        # Verify session belongs to user
        session = await cosmos_manager.get_session(session_id)
        if not session or session.user_id != user_id:
            raise HTTPException(status_code=404, detail="Session not found")

        # Set cookies for the session
        response = JSONResponse(content={"message": "Switched to session successfully"})
        response.set_cookie("session_id", session.session_id)
        response.set_cookie("thread_id", session.thread_id)
        response.set_cookie("agent_id", session.agent_id)
        response.set_cookie("user_id", user_id)

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error switching to session: {e}")
        raise HTTPException(status_code=500, detail="Failed to switch to session")

# Catch-all route for React client-side routing (must be last)
@router.get("/{path:path}", response_class=HTMLResponse)
async def catch_all_spa_routes(request: Request, path: str, _ = auth_dependency):
    # Only serve React app for specific client-side routes
    if path in ["chat", "agents"] or path.startswith("chat/") or path.startswith("agents/"):
        return templates.TemplateResponse(
            "index.html",
            {
                "request": request,
            }
        )
    # For all other paths, return 404
    raise HTTPException(status_code=404, detail="Not found")