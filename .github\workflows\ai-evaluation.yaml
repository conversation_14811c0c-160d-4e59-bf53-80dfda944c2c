name: "AI Agent Evaluation"

on:
  workflow_dispatch:
  push:
    branches:
      - main

permissions:
  id-token: write
  contents: read

jobs:
  run-action:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Azure login using Federated Credentials
        uses: azure/login@v2
        with:
          client-id: ${{ vars.AZURE_CLIENT_ID }}
          tenant-id: ${{ vars.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.AZURE_SUBSCRIPTION_ID }}

      - name: Run Evaluation
        uses: microsoft/ai-agent-evals@v2-beta
        with:
          azure-ai-project-endpoint: ${{ vars.AZURE_EXISTING_AIPROJECT_ENDPOINT }}
          deployment-name: ${{ vars.AZURE_AI_AGENT_DEPLOYMENT_NAME }}
          agent-ids: ${{ vars.AZURE_EXISTING_AGENT_ID }}
          data-path: ${{ github.workspace }}/evals/eval-action-data-path.json