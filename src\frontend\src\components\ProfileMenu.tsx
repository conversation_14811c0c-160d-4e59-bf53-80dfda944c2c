import React, { useState } from 'react';
import ProfileAvatar from './ProfileAvatar';
import { IMenuItemConfig } from './core/MenuButton/MenuButton';

interface ProfileMenuProps {
  src?: string;
  alt?: string;
  menuItems: IMenuItemConfig[];
}

const ProfileMenu: React.FC<ProfileMenuProps> = ({ 
  src = 'https://placehold.co/60x60/160211/ffffff?text=U',
  alt = 'User Avatar',
  menuItems 
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleAvatarClick = () => {
    setIsOpen(!isOpen);
  };

  const handleMenuItemClick = (itemOnClick?: (event: React.MouseEvent<HTMLButtonElement>) => void) => {
    setIsOpen(false);
    if (itemOnClick) {
      itemOnClick({} as React.MouseEvent<HTMLButtonElement>);
    }
  };

  return (
    <div className="relative">
      {/* Backdrop to close menu when clicking outside */}
      {isOpen && (
        <div
          className="fixed inset-0 z-10"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Profile Avatar */}
      <ProfileAvatar 
        src={src}
        alt={alt}
        onClick={handleAvatarClick}
      />

      {/* Dropdown Menu */}
      {isOpen && (
        <div 
          className="absolute bottom-3 left-[75px] mb-2 z-20 min-w-48 rounded-fluent shadow-lg"
          style={{
            backgroundColor: 'var(--colorNeutralBackground1)',
            border: '1px solid var(--colorNeutralStroke1)'
          }}
        >
          {menuItems.map(({ key, children, onClick: itemOnClick, disabled, className: itemClassName }) => (
            <button
              key={key}
              type="button"
              className={`w-full px-fluent-md py-fluent-sm text-left text-body1 hover:bg-opacity-10 focus:outline-none ${
                disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
              } ${itemClassName || ''}`}
              style={{
                color: 'var(--colorNeutralForeground1)',
              }}
              onClick={() => {
                if (!disabled) {
                  handleMenuItemClick(itemOnClick);
                }
              }}
              disabled={disabled}
              onMouseEnter={(e) => {
                if (!disabled) {
                  e.currentTarget.style.backgroundColor = 'var(--colorNeutralBackground2)';
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              {children}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProfileMenu;
