[project]
name = "api"
version = "1.0.0"
description = "Create a simple chat app using Quart and Azure AI Agents"
dependencies = [
    "fastapi==0.111.0",
    "gunicorn==22.0.0",
    "uvicorn[standard]==0.29.0",
    "opentelemetry-sdk",
    "azure-identity==1.19.0",
    "aiohttp==3.11.1",
    "azure-ai-projects",
    "azure-core-tracing-opentelemetry",
    "azure-monitor-opentelemetry>=1.6.9",
    "azure-search-documents"
    ]

[build-system]
requires = ["flit_core<4"]
build-backend = "flit_core.buildapi"
