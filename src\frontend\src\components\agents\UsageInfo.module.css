.usageInfoContainer {
  position: relative;
  font-size: 12px;
  color: var(--colorNeutralForeground2);
}

.usageSummary {
  display: flex;
  align-items: center;
  gap: 8px;
}

.divider {
  color: var(--colorNeutralForeground3);
}

.infoButton {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--colorBrandForeground1);
}

.infoIcon {
  font-size: 14px;
}

.usageDetails {
  display: none;
  position: absolute;
  bottom: 24px;
  right: 0;
  background-color: var(--colorNeutralBackground1);
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 12px;
  width: 200px;
  z-index: 100;
}

.infoButton:focus + .usageDetails,
.infoButton:hover + .usageDetails,
.usageDetails:hover {
  display: block;
}

.detailsDivider {
  margin: 8px 0;
}

.detailsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detailsItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detailLabel {
  font-size: 12px;
  color: var(--colorNeutralForeground2);
}

.detailValue {
  font-size: 12px;
  font-weight: 600;
  color: var(--colorNeutralForeground1);
}