import { Info } from 'lucide-react';

import styles from './UsageInfo.module.css';

interface ITokenUsageInfo {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

interface IUsageInfoProps {
  info: ITokenUsageInfo;
  duration?: number;
}

export function UsageInfo({ info, duration }: IUsageInfoProps): React.ReactElement {
  return (
    <div className={styles.usageInfoContainer}>
      <div className={styles.usageSummary}>
        {duration !== undefined && (
          <>
            <span>{`${duration.toFixed(0)}ms`}</span>
            <span className={styles.divider}>|</span>
          </>
        )}
        <span>{`${info.total_tokens} tokens`}</span>
        <button
          className={styles.infoButton}
          title="Usage information"
        >
          <Info className={styles.infoIcon} />
        </button>
      </div>
      <div className={styles.usageDetails}>
        <span className="text-sm font-semibold">Usage Information</span>
        <div className={styles.detailsDivider} style={{ height: '1px', backgroundColor: 'var(--colorNeutralStroke1)', margin: '8px 0' }} />
        <div className={styles.detailsList}>
          <div className={styles.detailsItem}>
            <span className={`${styles.detailLabel} text-body1`}>Input</span>
            <span className={`${styles.detailValue} text-body1`}>{info.prompt_tokens}</span>
          </div>
          <div className={styles.detailsItem}>
            <span className={`${styles.detailLabel} text-body1`}>Output</span>
            <span className={`${styles.detailValue} text-body1`}>{info.completion_tokens}</span>
          </div>
        </div>
      </div>
    </div>
  );
}