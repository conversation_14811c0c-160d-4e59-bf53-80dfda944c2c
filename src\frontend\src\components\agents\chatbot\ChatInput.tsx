import React, { useState, useEffect, useRef } from "react";
import { ChatInputProps } from "./types";
import SuggestionBoxes from "./SuggestionBoxes";

export const ChatInput: React.FC<ChatInputProps> = ({
  onSubmit,
  isGenerating,
  currentUserMessage,
  showSuggestions = false,
}) => {
  const [inputText, setInputText] = useState<string>("");
  const [suggestionsVisible, setSuggestionsVisible] = useState<boolean>(showSuggestions);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (currentUserMessage !== undefined) {
      setInputText(currentUserMessage ?? "");
    }
  }, [currentUserMessage]);

  useEffect(() => {
    setSuggestionsVisible(showSuggestions);
  }, [showSuggestions]);

  const onMessageSend = (text: string): void => {
    if (text && text.trim() !== "") {
      onSubmit(text.trim());
      setInputText("");
      setSuggestionsVisible(false); // Hide suggestions after sending a message
    }
  };

  const handleSuggestionClick = (suggestion: string): void => {
    onMessageSend(suggestion);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isGenerating) {
      onMessageSend(inputText);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isGenerating) {
        onMessageSend(inputText);
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputText(e.target.value);

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  return (
    <div>
      <SuggestionBoxes
        onSuggestionClick={handleSuggestionClick}
        isVisible={suggestionsVisible}
      />
      <form onSubmit={handleSubmit} className="p-3" style={{ borderColor: 'var(--colorNeutralStroke1)' }}>
        <div className="relative flex">
          <textarea
            ref={textareaRef}
            value={inputText}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder="Type your message here..."
            disabled={isGenerating}
            className="w-full min-h-[40px] max-h-32 pl-3 pr-12 py-2 resize-none rounded-fluent border focus:outline-none"
            style={{
              backgroundColor: 'var(--colorNeutralBackground1)',
              borderColor: 'var(--colorNeutralStroke1)',
              color: 'var(--colorNeutralForeground1)',
            }}
            data-testid="chat-input"
            aria-label="Chat Input"
            rows={1}
          />
          <button
            type="submit"
            disabled={isGenerating || !inputText.trim()}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-full transition-opacity duration-200"
            style={{
              backgroundColor: isGenerating || !inputText.trim() ? 'transparent' : '',
              opacity: isGenerating || !inputText.trim() ? 0.5 : 1,
            }}
            aria-label="Send message"
          >
            {isGenerating ? (
              <div className="spinner spinner-small" />
            ) : (
              <svg width="26" height="25" viewBox="0 0 26 25" fill="#45628880" xmlns="http://www.w3.org/2000/svg">
                <path d="M25.0569 12.1215L0.978837 0.0479978C0.88097 -0.000941201 0.768711 -0.0124562 0.662209 0.0134527C0.544007 0.0426773 0.442201 0.117554 0.37908 0.221688C0.31596 0.325822 0.296669 0.450727 0.325432 0.569054L2.80664 10.7081C2.84406 10.8606 2.95632 10.9844 3.106 11.0334L7.35745 12.4929L3.10888 13.9524C2.9592 14.0043 2.84694 14.1252 2.8124 14.2777L0.325432 24.4311C0.299526 24.5377 0.31104 24.6499 0.359974 24.7449C0.472233 24.9724 0.748562 25.0645 0.978837 24.9522L25.0569 12.9477C25.1462 12.9046 25.2181 12.8297 25.2642 12.7434C25.3765 12.5131 25.2843 12.2367 25.0569 12.1215ZM3.16357 21.5466L4.61142 15.6279L13.1086 12.7117C13.1748 12.6887 13.2295 12.6368 13.2525 12.5678C13.2928 12.4468 13.2295 12.3173 13.1086 12.2741L4.61142 9.36081L3.16933 3.46509L21.2459 12.5303L3.16357 21.5466Z" fill="#456288" fill-opacity="0.5"/>
              </svg>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ChatInput;
