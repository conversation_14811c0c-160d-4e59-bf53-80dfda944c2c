import { Edit, User } from "lucide-react";
import { Suspense } from "react";

import { useFormatTimestamp } from "./hooks/useFormatTimestamp";
import { IUserMessageProps } from "./chatbot/types";

import { Markdown } from "../core/Markdown";

import styles from "./AgentPreviewChatBot.module.css";



export function UserMessage({
  message,
  onEditMessage,
}: IUserMessageProps): JSX.Element {
  const formatTimestamp = useFormatTimestamp();

  return (
    <div
      key={message.id}
      className={`${styles.userMessage} flex gap-3 p-4 mb-4 justify-end`}
    >
      {/* Message Content */}
      <div className="flex-1 max-w-3xl">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium" style={{ color: 'var(--colorNeutralForeground1)' }}>
              You
            </span>
            {message.more?.time && (
              <span className="text-xs" style={{ color: 'var(--colorNeutralForeground3)' }}>
                {formatTimestamp(new Date(message.more.time))}
              </span>
            )}
          </div>
          <button
            className="btn-subtle p-1"
            aria-label="Edit"
            onClick={() => {
              onEditMessage(message.id);
            }}
          >
            <Edit className="w-4 h-4" aria-hidden={true} />
          </button>
        </div>

        <div
          className="p-3 rounded-fluent"
          style={{
            padding: '10px',
            background: 'rgba(255, 255, 255, 0.50)',
            borderRadius: '8px',
            outline: '1px white solid',
            outlineOffset: '-1px',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '10px',
            display: 'inline-flex',
            width: '100%'
          }}
        >
          <Suspense fallback={<div className="spinner spinner-small" />}>
            <Markdown content={message.content} />
          </Suspense>
        </div>
      </div>

      {/* Avatar */}
      <div className="flex-shrink-0">
        <div
          className="w-8 h-8 rounded-full flex items-center justify-center"
          style={{ backgroundColor: '#160211' }}
        >
          <User className="w-4 h-4 text-white" />
        </div>
      </div>
    </div>
  );
}
