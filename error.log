Connected!
2025-09-29T01:09:14.659675201Z     _____                               
icrosoft.com/appsvc/msitokenservice:stage5

2025-09-29T01:09:14.659738813Z    /  _  \ __________ _________   ____  
icrosoft.com/appsvc/msitokenservice:stage5

2025-09-29T01:09:14.659742434Z   /  /_\  \\___   /  |  \_  __ \_/ __ \ 
icrosoft.com/appsvc/msitokenservice:stage5

2025-09-29T01:09:14.659744996Z  /    |    \/    /|  |  /|  | \/\  ___/ 
icrosoft.com/appsvc/msitokenservice:stage5

2025-09-29T01:09:14.659746992Z  \____|__  /_____ \____/ |__|    \___  >
icrosoft.com/appsvc/msitokenservice:stage5

2025-09-29T01:09:14.659749213Z          \/      \/                  \/ 
icrosoft.com/appsvc/msitokenservice:stage5

2025-09-29T01:09:14.659751108Z  A P P   S E R V I C E   O N   L I N U X
icrosoft.com/appsvc/msitokenservice:stage5

2025-09-29T01:09:14.659752978Z
2025-09-29T01:09:14.659754666Z  Documentation: http://aka.ms/webapp-linux
rosoft.com/appsvc/msitokenservice:stage5

2025-09-29T01:09:14.659756624Z  Python 3.13.5
tificates

2025-09-29T01:09:14.659758389Z  Note: Any data outside '/home' is not persisted
.com/appsvc/msitokenservice:stage5

2025-09-29T01:09:15.673945114Z  Starting OpenBSD Secure Shell server: sshd.

2025-09-29T01:09:15.686043132Z  WEBSITES_INCLUDE_CLOUD_CERTS is not set to true.

2025-09-29T01:09:15.693955689Z  Updating certificates in /etc/ssl/certs...
true.

2025-09-29T01:09:18.339162970Z  rehash: warning: skipping ca-certificates.crt,it does not contain exactly one certificate or CRL
2025-09-29T01:09:18.366914757Z  4 added, 0 removed; done.

2025-09-29T01:09:18.366937866Z  Running hooks in /etc/ca-certificates/update.d...
oes not contain exactly one certificate or CRL
2025-09-29T01:09:18.368291761Z  done.

2025-09-29T01:09:18.378215940Z  CA certificates copied and updated successfully.

2025-09-29T01:09:18.416330846Z  Site's appCommandLine: cd src && python -m uvicorn api.main:create_app --factory --host 0.0.0.0 --port 8000

2025-09-29T01:09:18.416518539Z  Launching oryx with: create-script -appPath /home/<USER>/wwwroot -output /opt/startup/startup.sh -virtualEnvName antenv -defaultApp /opt/defaultsite -userStartupCommand 'cd src && python -m uvicorn api.main:create_app --factory --host 0.0.0.0 --port 8000'
BL?certificateRevocationList?base?objectClass=cRLDistributionPoint
2025-09-29T01:09:18.445545936Z  Found build manifest file at '/home/<USER>/wwwroot/oryx-manifest.toml'. Deserializing it...

2025-09-29T01:09:18.446556920Z  Build Operation ID: a71d40c656a1898c
ite/wwwroot/oryx-manifest.toml'. Deserializing it...

2025-09-29T01:09:18.447161583Z  Oryx Version: 0.2.20250709.3, Commit: 0cfb8cb7d114b65e539d2f9ccf9804e87b9966ba, ReleaseTagName: 20250709.3


2025-09-29T01:09:18.447321114Z  Output is compressed. Extracting it...
e/wwwroot/oryx-manifest.toml'. Deserializing it...

2025-09-29T01:09:18.447904117Z  Extracting '/home/<USER>/wwwroot/output.tar.gz' to directory '/tmp/8ddfef4710550f0'...
t...

2025-09-29T01:09:22.328044089Z  App path is set to '/tmp/8ddfef4710550f0'
te.d...
oes not contain exactly one certificate or CRL
2025-09-29T01:09:22.397579404Z  Writing output script to '/opt/startup/startup.sh'
m/appsvc/msitokenservice:stage5

2025-09-29T01:09:22.477279986Z  Using packages from virtual environment antenv located at /tmp/8ddfef4710550f0/antenv.

2025-09-29T01:09:22.477317812Z  Updated PYTHONPATH to '/opt/startup/app_logs:/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages'
lapsed time for Task.WhenAny(): 6 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:31.489767318Z  2025-09-29 01:09:31,489 [INFO] azureaiapp: Tracing is not enabled
e":"Entering app startup."}

2025-09-29T01:09:32.167104882Z  INFO:     Started server process [1114]
p: Tracing is not enabled
e":"Entering app startup."}

2025-09-29T01:09:32.167165495Z  INFO:     Waiting for application startup.
Tracing is not enabled
e":"Entering app startup."}

2025-09-29T01:09:32.178246257Z  2025-09-29 01:09:32,177 [INFO] azureaiapp: Created AIProjectClient
dfef4710550f0'...
t...

2025-09-29T01:09:35.169802844Z  2025-09-29 01:09:35,160 [ERROR] azureaiapp: Error fetching agent: (PermissionDenied) The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants/{assistantId}` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.

2025-09-29T01:09:35.169841980Z  Code: PermissionDenied

2025-09-29T01:09:35.169845583Z  Message: The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants/{assistantId}` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.
 granting the necessary permissions, see https://aka.ms/FoundryPermissions.

2025-09-29T01:09:35.169849016Z  Traceback (most recent call last):
tartup.
Tracing is not enabled
e":"Entering app startup."}

2025-09-29T01:09:35.169851356Z    File "/tmp/8ddfef4710550f0/src/api/main.py", line 57, in lifespan
:"Entering app startup."}

2025-09-29T01:09:35.169853887Z      agent = await ai_project.agents.get_agent(agent_id)
in lifespan
:"Entering app startup."}

2025-09-29T01:09:35.169856463Z              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
in lifespan
:"Entering app startup."}

2025-09-29T01:09:35.169858857Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/core/tracing/decorator_async.py", line 119, in wrapper_use_tracer
/lga-ai-marketplace-webapp: Elapsed time for Task.WhenAny(): 7 ms

2025-09-29T01:09:35.169861140Z      return await func(*args, **kwargs)
^^^^^^^^^^^^^^^^
in lifespan
:"Entering app startup."}

2025-09-29T01:09:35.169863207Z             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^^^^^^^^^^^^^^^
in lifespan
:"Entering app startup."}

2025-09-29T01:09:35.169865340Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/ai/agents/aio/operations/_operations.py", line 4355, in get_agent
/lga-ai-marketplace-webapp: Elapsed time for Task.WhenAny(): 7 ms

2025-09-29T01:09:35.169867734Z      map_error(status_code=response.status_code, response=response, error_map=error_map)
up."}

2025-09-29T01:09:35.169870001Z      ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
up."}

2025-09-29T01:09:35.169872151Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/core/exceptions.py", line 163, in map_error
for Task.WhenAny(): 6 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.169874462Z      raise error
ficates

2025-09-29T01:09:35.169876785Z  azure.core.exceptions.ClientAuthenticationError: (PermissionDenied) The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants/{assistantId}` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.

2025-09-29T01:09:35.169879639Z  Code: PermissionDenied


2025-09-29T01:09:35.169882624Z  Message: The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants/{assistantId}` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.
essary permissions, see https://aka.ms/FoundryPermissions.

2025-09-29T01:09:35.232108265Z  2025-09-29 01:09:35,230 [ERROR] azureaiapp: Error during startup: (PermissionDenied) The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.

2025-09-29T01:09:35.232163696Z  Code: PermissionDenied


2025-09-29T01:09:35.232167086Z  Message: The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.
 granting the necessary permissions, see https://aka.ms/FoundryPermissions.

2025-09-29T01:09:35.232170037Z  Traceback (most recent call last):
ment antenv located at /tmp/8ddfef4710550f0/antenv.

2025-09-29T01:09:35.232172966Z    File "/tmp/8ddfef4710550f0/src/api/main.py", line 69, in lifespan
f4710550f0/antenv.

2025-09-29T01:09:35.232176132Z      async for agent_object in agent_list:
py", line 69, in lifespan
f4710550f0/antenv.

2025-09-29T01:09:35.232178902Z      ...<3 lines>...
ed


2025-09-29T01:09:35.232181260Z              break
.
ed


2025-09-29T01:09:35.232183201Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/core/async_paging.py", line 142, in __anext__
r Task.WhenAny(): 6 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.232185269Z      return await self.__anext__()
t_list:
py", line 69, in lifespan
f4710550f0/antenv.

2025-09-29T01:09:35.232187091Z             ^^^^^^^^^^^^^^^^^^^^^^
t_list:
py", line 69, in lifespan
f4710550f0/antenv.

2025-09-29T01:09:35.232188873Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/core/async_paging.py", line 145, in __anext__
r Task.WhenAny(): 6 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.232190833Z      self._page = await self._page_iterator.__anext__()
 in lifespan
f4710550f0/antenv.

2025-09-29T01:09:35.232192723Z                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
 in lifespan
f4710550f0/antenv.

2025-09-29T01:09:35.232194595Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/core/async_paging.py", line 94, in __anext__

r Task.WhenAny(): 6 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.232196565Z      self._response = await self._get_next(self.continuation_token)

f4710550f0/antenv.

2025-09-29T01:09:35.232198412Z                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

f4710550f0/antenv.

2025-09-29T01:09:35.232200385Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/ai/agents/aio/operations/_operations.py", line 4300, in get_next
 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.232202361Z      map_error(status_code=response.status_code, response=response, error_map=error_map)

2025-09-29T01:09:35.232204233Z      ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.232206075Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/core/exceptions.py", line 163, in map_error
ne 4300, in get_next
 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.232208053Z      raise error
k
.
ed


2025-09-29T01:09:35.232210571Z  azure.core.exceptions.ClientAuthenticationError: (PermissionDenied) The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.
ndryPermissions.

2025-09-29T01:09:35.232213043Z  Code: PermissionDenied


2025-09-29T01:09:35.232214861Z  Message: The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.
essary permissions, see https://aka.ms/FoundryPermissions.
ndryPermissions.

2025-09-29T01:09:35.232220704Z  2025-09-29 01:09:35,232 [INFO] azureaiapp: Closed CosmosDB client
^^^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.234321026Z  2025-09-29 01:09:35,232 [INFO] azureaiapp: Closed AIProjectClient
^^^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237500159Z  ERROR:    Traceback (most recent call last):
osed AIProjectClient
^^^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237521647Z    File "/tmp/8ddfef4710550f0/src/api/main.py", line 69, in lifespan
^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237524570Z      async for agent_object in agent_list:
py", line 69, in lifespan
^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237526704Z      ...<3 lines>...
ed


2025-09-29T01:09:35.237529015Z              break
.
ed


2025-09-29T01:09:35.237530917Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/core/async_paging.py", line 142, in __anext__
 4300, in get_next
 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.237533425Z      return await self.__anext__()
t_list:
py", line 69, in lifespan
^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237535433Z             ^^^^^^^^^^^^^^^^^^^^^^
t_list:
py", line 69, in lifespan
^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237537184Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/core/async_paging.py", line 145, in __anext__
 4300, in get_next
 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.237539138Z      self._page = await self._page_iterator.__anext__()
 in lifespan
^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237541315Z                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
 in lifespan
^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237543168Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/core/async_paging.py", line 94, in __anext__

 4300, in get_next
 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.237545095Z      self._response = await self._get_next(self.continuation_token)

^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237546987Z                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237548956Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/ai/agents/aio/operations/_operations.py", line 4300, in get_next
 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.237550981Z      map_error(status_code=response.status_code, response=response, error_map=error_map)

2025-09-29T01:09:35.237552803Z      ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237554713Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/azure/core/exceptions.py", line 163, in map_error
ne 4300, in get_next
 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.237556732Z      raise error
k
.
ed


2025-09-29T01:09:35.237558611Z  azure.core.exceptions.ClientAuthenticationError: (PermissionDenied) The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.
ndryPermissions.

2025-09-29T01:09:35.237561366Z  Code: PermissionDenied


2025-09-29T01:09:35.237563182Z  Message: The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.
essary permissions, see https://aka.ms/FoundryPermissions.
ndryPermissions.

2025-09-29T01:09:35.237576785Z
2025-09-29T01:09:35.237579273Z  During handling of the above exception, another exception occurred:
^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237581264Z
2025-09-29T01:09:35.237582971Z  Traceback (most recent call last):
ion, another exception occurred:
^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237584851Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/starlette/routing.py", line 692, in lifespan
rror
ne 4300, in get_next
 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.237586823Z      async with self.lifespan_context(app) as maybe_state:
occurred:
^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237588700Z                 ~~~~~~~~~~~~~~~~~~~~~^^^^^
as maybe_state:
occurred:
^^^^^^^^^^^^^^^^^^^

2025-09-29T01:09:35.237590711Z    File "/opt/python/3.13.5/lib/python3.13/contextlib.py", line 214, in __aenter__
^^^^^

2025-09-29T01:09:35.237592690Z      return await anext(self.gen)
2025-09-29T01:09:35.237594519Z             ^^^^^^^^^^^^^^^^^^^^^
2025-09-29T01:09:35.237596265Z    File "/tmp/8ddfef4710550f0/antenv/lib/python3.13/site-packages/fastapi/routing.py", line 133, in merged_lifespan
ne 4300, in get_next
 ms
Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage5"}

2025-09-29T01:09:35.237598826Z      async with original_context(app) as maybe_original_state:
 214, in __aenter__
^^^^^

2025-09-29T01:09:35.237600742Z                 ~~~~~~~~~~~~~~~~^^^^^
as maybe_original_state:
 214, in __aenter__
^^^^^

2025-09-29T01:09:35.237602571Z    File "/opt/python/3.13.5/lib/python3.13/contextlib.py", line 214, in __aenter__
^^^^^

2025-09-29T01:09:35.237604454Z      return await anext(self.gen)
2025-09-29T01:09:35.237606651Z             ^^^^^^^^^^^^^^^^^^^^^
2025-09-29T01:09:35.237608464Z    File "/tmp/8ddfef4710550f0/src/api/main.py", line 95, in lifespan
in __aenter__
^^^^^

2025-09-29T01:09:35.237610323Z      raise RuntimeError(f"Error during startup: {e}")
5, in lifespan
in __aenter__
^^^^^

2025-09-29T01:09:35.237612267Z  RuntimeError: Error during startup: (PermissionDenied) The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.
Permissions.
ndryPermissions.

2025-09-29T01:09:35.237614698Z  Code: PermissionDenied
^^^^^^^^^
2025-09-29T01:09:35.237616570Z  Message: The principal `fa762214-97b1-4c5f-b045-bd54bc42c99e` lacks the required data action `Microsoft.CognitiveServices/accounts/AIServices/agents/read` to perform `GET /api/projects/{projectName}/assistants` operation. For instructions on granting the necessary permissions, see https://aka.ms/FoundryPermissions.
sions, see https://aka.ms/FoundryPermissions.
Permissions.
ndryPermissions.

2025-09-29T01:09:35.237618860Z
2025-09-29T01:09:35.237620559Z  ERROR:    Application startup failed. Exiting.
{e}")
5, in lifespan
in __aenter__
^^^^^