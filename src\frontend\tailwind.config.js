/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Brand colors extracted from FluentUI theme
        brand: {
          10: "#010306",
          20: "#071926",
          30: "#002A41",
          40: "#003653",
          50: "#004365",
          60: "#005078",
          70: "#005E8B",
          80: "#007BB4",
          90: "#007BB4",
          100: "#008AC9",
          110: "#0099DE",
          120: "#00A8F4",
          130: "#3FB6FF",
          140: "#73C3FF",
          150: "#98D0FF",
          160: "#B8DEFF",
        },
        // FluentUI semantic colors for light theme
        light: {
          background: {
            1: "#ffffff",
            2: "#fafafa",
            3: "#f5f5f5",
            4: "#e0e0e0",
            disabled: "#f3f2f1",
          },
          foreground: {
            1: "#323130",
            2: "#605e5c",
            3: "#8a8886",
            disabled: "#a19f9d",
          },
          stroke: {
            1: "#d2d0ce",
            2: "#edebe9",
            disabled: "#f3f2f1",
          }
        },
        // FluentUI semantic colors for dark theme
        dark: {
          background: {
            1: "#1b1a19",
            2: "#252423",
            3: "#323130",
            4: "#484644",
            disabled: "#3b3a39",
          },
          foreground: {
            1: "#ffffff",
            2: "#f3f2f1",
            3: "#d2d0ce",
            disabled: "#797775",
          },
          stroke: {
            1: "#605e5c",
            2: "#484644",
            disabled: "#3b3a39",
          }
        }
      },
      fontFamily: {
        'segoe': ['Segoe UI', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
      },
      fontSize: {
        'caption1': ['12px', '16px'],
        'body1': ['14px', '20px'],
        'subtitle1': ['16px', '22px'],
        'title1': ['20px', '28px'],
      },
      borderRadius: {
        'fluent': '8px',
        'fluent-lg': '8px',
      },
      spacing: {
        'fluent-xs': '4px',
        'fluent-sm': '8px',
        'fluent-md': '12px',
        'fluent-lg': '16px',
        'fluent-xl': '20px',
        'fluent-2xl': '24px',
      }
    },
  },
  plugins: [],
  darkMode: 'class',
}
