/* ===== Nav / Pill Bar ===== */
.navWrap {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  display: flex; 
  justify-content: center;
  z-index: 10;
}

.nav {
  --h: 50px; 
  height: var(--h);
  background: var(--ink, #160211);
  border-radius: calc(var(--h) / 2);
  padding: 0 40px; 
  gap: 90px; 
  display: flex; 
  align-items: center;
  color: #fff; 
  width: min(962px, calc(100% - 32px));
  outline: 1px solid var(--ring, rgba(255,255,255,.9)); 
  outline-offset: -1px;
  box-shadow: 0 2px 0 rgba(0,0,0,.02);
}

.nav a {
  color: #fff; 
  text-decoration: none; 
  font-size: 16px; 
  line-height: 1; 
  padding: 8px 16px; 
  border-radius: 9999px;
  transition: background-color 0.2s ease;
}

.nav a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav a:focus-visible { 
  outline: 2px solid #fff; 
  outline-offset: 2px; 
}

.nav a.active {
  background-color: rgba(255, 255, 255, 0.15);
  font-weight: 500;
}

.spacer { 
  flex: 1; 
}

.start {
  display: inline-flex; 
  align-items: center; 
  gap: 10px; 
  margin-left: auto;
  padding: 10px 14px; 
  border-radius: 9999px; 
  white-space: nowrap;
  transition: background-color 0.2s ease;
}

.start:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.start svg { 
  width: 22px; 
  height: 22px; 
  fill: #fff; 
}

@media (max-width: 480px) {
  .nav { 
    gap: 8px; 
    padding: 0 10px; 
  }
  
  .hideSm { 
    display: none; 
  }
}
