import React from 'react';
import styles from './AgentCard.module.css';

interface AgentCardProps {
  label: string;
  onClick?: () => void;
  className?: string;
}

const AgentCard: React.FC<AgentCardProps> = ({
  label,
  onClick,
  className = ''
}) => {
  return (
    <div className={`${styles.feature} ${className}`}>
      <a
        href="#"
        onClick={(e) => {
          e.preventDefault();
          if (onClick) onClick();
        }}
        className={styles.featureLink}
      >
        <div className={styles.badge} aria-hidden="true">
          <svg width="45" height="45" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.379 1.1366C16.1762 0.718904 17.0801 0.499023 18.0002 0.499023C18.9204 0.499023 19.8244 0.718922 20.6216 1.13665L20.6252 1.13857L32.8753 7.50338C33.6726 7.92195 34.3348 8.52379 34.7955 9.24854C35.2563 9.97328 35.4993 10.7955 35.5003 11.6326V24.3655C35.4993 25.2026 35.2563 26.0248 34.7955 26.7495C34.3348 27.4743 33.6726 28.0761 32.8753 28.4947L32.8685 28.4982L20.6252 34.8595L20.6221 34.8611C19.8248 35.279 18.9206 35.499 18.0002 35.499C17.0799 35.499 16.1758 35.2791 15.3785 34.8612L15.3752 34.8595L3.132 28.4982L3.12524 28.4947C2.32793 28.0761 1.66569 27.4743 1.20496 26.7495C0.744219 26.0248 0.501188 25.2026 0.500244 24.3655V11.6326C0.501188 10.7955 0.744219 9.97328 1.20496 9.24854C1.66569 8.52379 2.32793 7.92195 3.12524 7.50339L3.13199 7.49985L15.379 1.1366ZM18.0002 3.68144C17.6931 3.68144 17.3913 3.75496 17.1252 3.89462L17.1185 3.89816L4.87524 10.2594L4.87249 10.2609C4.60795 10.4003 4.38819 10.6004 4.23515 10.8412C4.08169 11.0825 4.00069 11.3563 4.00024 11.6351V24.3629C4.00069 24.6417 4.08169 24.9155 4.23515 25.1569C4.38819 25.3976 4.60795 25.5977 4.87249 25.7372L4.87524 25.7386L17.1253 32.1034C17.3913 32.2431 17.6931 32.3166 18.0002 32.3166C18.3074 32.3166 18.6092 32.2431 18.8752 32.1034L18.882 32.0999L31.1252 25.7386L31.128 25.7372C31.3926 25.5977 31.6123 25.3976 31.7653 25.1569C31.9189 24.9153 31.9999 24.6413 32.0002 24.3622V11.6358C31.9999 11.3568 31.9189 11.0827 31.7653 10.8412C31.6123 10.6004 31.3926 10.4004 31.128 10.2609L31.1252 10.2594L18.8752 3.89463C18.6092 3.75497 18.3074 3.68144 18.0002 3.68144Z" fill="white" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M16.3311 8.6944C16.5593 8.53699 16.8379 8.45179 17.1243 8.45179C17.4108 8.45179 17.6894 8.53699 17.9176 8.6944C18.1458 8.8518 18.3111 9.07276 18.3883 9.32357L18.393 9.33945L19.7763 14.2203C19.8154 14.3579 19.8943 14.4836 20.0048 14.5841C20.1154 14.6846 20.2534 14.7563 20.4048 14.7919L25.7729 16.0497L25.7872 16.0532C26.0641 16.1226 26.3084 16.2728 26.4824 16.4806C26.6565 16.6884 26.7507 16.9424 26.7507 17.2036C26.7507 17.4648 26.6565 17.7188 26.4824 17.9266C26.3084 18.1345 26.0641 18.2846 25.7872 18.3541L25.7729 18.3575L20.405 19.6153C20.2536 19.6508 20.1154 19.7226 20.0048 19.8231C19.8943 19.9237 19.8154 20.0493 19.7763 20.187L18.3921 25.0679L18.3874 25.0837C18.3102 25.3345 18.1449 25.5554 17.9167 25.7129C17.6885 25.8703 17.4099 25.9555 17.1235 25.9555C16.837 25.9555 16.5585 25.8703 16.3303 25.7129C16.102 25.5554 15.9367 25.3345 15.8595 25.0837L15.8548 25.0678L14.4715 20.187C14.4324 20.0494 14.3535 19.9236 14.243 19.8231C14.1324 19.7226 13.9942 19.6508 13.8428 19.6153L8.4748 18.3567L8.45437 18.3517C8.17961 18.2808 7.93779 18.1303 7.7656 17.9231C7.59341 17.7159 7.50024 17.4633 7.50024 17.2036C7.50024 16.9439 7.59341 16.6913 7.7656 16.4841C7.93779 16.2769 8.17961 16.1265 8.45437 16.0556L8.47467 16.0506L13.8428 14.7911C13.9942 14.7556 14.1325 14.6839 14.243 14.5835C14.3535 14.483 14.4324 14.3576 14.4716 14.2201L15.8558 9.33933L15.8604 9.32357C15.9376 9.07276 16.1029 8.8518 16.3311 8.6944ZM17.1242 11.2391L18.0817 14.6176C18.1989 15.0306 18.4357 15.4077 18.7674 15.7093C19.0991 16.0109 19.5136 16.2261 19.9678 16.3327L23.6848 17.2036L19.9681 18.0745C19.5138 18.1811 19.0991 18.3964 18.7674 18.698C18.4357 18.9996 18.199 19.3765 18.0818 19.7895L17.1236 23.1681L16.1661 19.7897C16.0489 19.3767 15.8121 18.9996 15.4804 18.698C15.1487 18.3964 14.7342 18.1811 14.28 18.0746L10.5649 17.2035L14.2799 16.3319C14.7339 16.2255 15.1484 16.0103 15.48 15.709C15.8117 15.4076 16.0485 15.031 16.1659 14.6182L17.1242 11.2391Z" fill="white" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M24.1239 9.24759C24.6072 9.24759 24.9989 9.6038 24.9989 10.0432V13.2256C24.9989 13.665 24.6072 14.0212 24.1239 14.0212C23.6407 14.0212 23.2489 13.665 23.2489 13.2256V10.0432C23.2489 9.6038 23.6407 9.24759 24.1239 9.24759Z" fill="white" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M21.4989 11.6344C21.4989 11.195 21.8907 10.8388 22.3739 10.8388H25.8739C26.3572 10.8388 26.7489 11.195 26.7489 11.6344C26.7489 12.0738 26.3572 12.43 25.8739 12.43H22.3739C21.8907 12.43 21.4989 12.0738 21.4989 11.6344Z" fill="white" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M10.1239 20.386C10.6072 20.386 10.9989 20.7422 10.9989 21.1816V22.7728C10.9989 23.2122 10.6072 23.5684 10.1239 23.5684C9.64066 23.5684 9.24891 23.2122 9.24891 22.7728V21.1816C9.24891 20.7422 9.64066 20.386 10.1239 20.386Z" fill="white" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.37391 21.9772C8.37391 21.5378 8.76566 21.1816 9.24891 21.1816H10.9989C11.4822 21.1816 11.8739 21.5378 11.8739 21.9772C11.8739 22.4166 11.4822 22.7728 10.9989 22.7728H9.24891C8.76566 22.7728 8.37391 22.4166 8.37391 21.9772Z" fill="white" />
          </svg>
        </div>
        <div className={styles.label}>{label}</div>
      </a>
    </div>
  );
};

export default AgentCard;
